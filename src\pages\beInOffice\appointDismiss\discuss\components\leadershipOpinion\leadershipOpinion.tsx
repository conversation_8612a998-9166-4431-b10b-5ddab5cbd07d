import React, { useState, useEffect, useCallback } from 'react';
import { Button, Input, Space, Modal, message, Spin, Select, Divider, Upload } from 'antd';
import moment from 'moment';
import styles from './index.less';
import TimePicker from '@/components/TimePicker';
import DictSelect from '@/components/DictSelect';
import { DeleteOutlined, PlusOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import request from '@/utils/request';
import { fileDownload } from '@/utils/method';
import { _history } from '@/utils/session';

const { Option } = Select;

// 生成唯一ID
const generateUniqueId = () => {
  return `id_${Date.now()}_${Math.random()
    .toString(36)
    .substr(2, 9)}`;
};

// 定义人员数据接口
interface PersonItem {
  code: string;
  selectedPositionCode?: string;
  selectedPositionName?: string;
  selectedCandidateCode?: string;
  selectedCandidateName?: string;
  positionCode?: string;
  positionName?: string;
  jobChange?: string;
  jobChangeCode?: string;
  currentPosition?: string;
  proposePosition?: string;
  proposedRemovalPosition?: string;
  [key: string]: any;
}

// 定义沟通表接口
interface CommunicateTable {
  code: string;
  cityLeader?: string;
  time?: string;
  typeCode?: string;
  list: RowItem[];
  [key: string]: any;
}

// 定义行数据接口
interface RowItem {
  id?: string; // 本地ID，用于React key
  code?: string; // 行记录code
  opinionCode?: string; // 沟通表code
  order?: number; // 序号
  name?: string | string[]; // 姓名
  personCode: string | string[]; // 人员code
  proposePosition?: string; // 推荐职务
  proposedRemovalPosition?: string; // 拟免职务
  currentPosition?: string; // 现任职务
  currentPositionTime?: string; // 现任职务任职时间
  currentPositionLevelTime?: string; // 任现职级时间
  A0104?: string; // 性别
  A0117A?: string; // 民族
  A0107?: string; // 出生年月
  A0111A?: string; // 籍贯
  feedbackOpinion?: string; // 反馈意见
  remarks?: string; // 备注
  program?: string; // 程序
  programCode?: string; // 程序代码
  url?: string  // 文件路径
}

const SeekPage = props => {
  const { pageType } = props;
  // 状态管理
  const [communicateTables, setCommunicateTables] = useState<CommunicateTable[]>([]); // 沟通表数组
  const [loading, setLoading] = useState<boolean>(false);
  const [planCode, setPlanCode] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');
  const [personList, setPersonList] = useState<PersonItem[]>([]); // 人员列表
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const querys = _history.location.query || {};
  const [importLoading, setImportLoading] = useState(false);
  const [fileUrl, setFileUrl] = useState(querys?.fileUrl || ''); // 文件下载路径
  const [htmlContent, setHtmlContent]: any = useState(sessionStorage.getItem('archiveMaterialsFile') || ''); // 从sessionStorage获取html内容

  // 从URL获取参数
  useEffect(() => {
    const query = _history.location.query || {};
    const code = query.code as string;
    const name = query.planName as string;

    if (code) {
      setPlanCode(code);
      loadData(code); // 加载人员下拉数据
      loadCommunicateTables(code); // 加载沟通表数据
    }

    if (name) {
      setPlanName(name);
    } else {
      setPlanName('推荐考察前与市领导沟通情况表');
    }
  }, []);

  // 加载人员下拉数据
  const loadData = useCallback(async (code: string) => {
    setLoading(true);
    try {
      let url = `/api/swzzbappoint/prepareCadres/findPersonByStatus?planCode=${code}&status=1`;
      const res = await request(url);
      if (res && res.code === 0 && res.data) {
        const data = res.data;

        // 处理人员数据作为下拉数据源
        if (data && Array.isArray(data)) {
          setPersonList(data);
        } else {
          message.warning('暂无人员数据');
        }
      } else {
        message.error(res?.message || '加载人员数据失败');
      }
    } catch (error) {
      console.error('加载人员数据出错:', error);
      message.error('加载人员数据出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载沟通表数据
  const loadCommunicateTables = useCallback(async (code: string) => {
    setLoading(true);
    try {
      const res = await request(`/api/swzzbappoint/opinion/findOpinion  `, {
        method: 'POST',
        body: {
          data: {
            planCode: code,
            tableType: pageType === 'cityLeader' ? 1 : 2, //1-市领导意见 2-市委委员意见
          },
        },
      });

      if (res && res.code === 0 && res.data) {
        const data = res.data;

        // 处理沟通表数据
        if (data && Array.isArray(data) && data.length > 0) {
          // 转换数据格式
          const formattedTables = data.map(table => {
            const communicateData = table.opinion || {};
            const contentListData = table.contentList || [];

            // 处理每个沟通表的contentList
            const formattedContentList = contentListData.map((item: any, index: number) => ({
              id: generateUniqueId(), // 本地ID，用于React key
              code: item.code || '',
              opinionCode: item.opinionCode || `defaultCode_${+new Date()}`,
              order: index + 1,
              name: item.name || '',
              proposePosition: item.proposePosition || '',
              proposedRemovalPosition: item.proposedRemovalPosition || '',
              currentPosition: item.currentPosition || '',
              currentPositionTime: item.currentPositionTime || '',
              currentPositionLevelTime: item.currentPositionLevelTime || '',
              A0104: item.A0104 || item.a0104 || '',
              A0117A: item.A0117A || item.a0117A || '',
              A0107: item.A0107 || item.a0107 || '',
              A0111A: item.A0111A || item.a0111A || '',
              feedbackOpinion: item.feedbackOpinion || '',
              remarks: item.remarks || '',
              personCode: item.personCode || '',
            }));

            // 如果没有contentList，添加一个空行
            if (formattedContentList.length === 0) {
              formattedContentList.push({
                id: generateUniqueId(),
                code: '',
                opinionCode: `defaultCode_${+new Date()}`,
                order: 1,
                name: '',
                personCode: '',
                proposePosition: '',
                proposedRemovalPosition: '',
                currentPosition: '',
                currentPositionTime: '',
                currentPositionLevelTime: '',
                A0104: '',
                A0117A: '',
                A0107: '',
                A0111A: '',
                feedbackOpinion: '',
                remarks: '',
              });
            }

            // 返回格式化后的沟通表
            return {
              code: communicateData.code || `defaultCode_${+new Date()}`,
              cityLeader: communicateData.cityLeader || '',
              time: communicateData.time || '',
              typeCode: communicateData.typeCode || '',
              list: formattedContentList,
            };
          });

          // 设置沟通表数据
          setCommunicateTables(formattedTables);
          console.log('formattedTables====', formattedTables);
        } else {
          // 如果没有沟通表数据，创建一个空的沟通表
          setCommunicateTables([
            {
              code: `defaultCode_${+new Date()}`,
              cityLeader: '',
              time: '',
              typeCode: '',
              list: [
                {
                  id: generateUniqueId(),
                  code: '',
                  opinionCode: `defaultCode_${+new Date()}`,
                  order: 1,
                  name: '',
                  personCode: '',
                  proposePosition: '',
                  proposedRemovalPosition: '',
                  currentPosition: '',
                  currentPositionTime: '',
                  currentPositionLevelTime: '',
                  A0104: '',
                  A0117A: '',
                  A0107: '',
                  A0111A: '',
                  feedbackOpinion: '',
                  remarks: '',
                },
              ],
            },
          ]);
        }
      } else {
        message.error(res?.message || '加载沟通表失败');
        // 设置默认空的沟通表
        setCommunicateTables([
          {
            code: `defaultCode_${+new Date()}`,
            cityLeader: '',
            time: '',
            typeCode: '',
            list: [
              {
                id: generateUniqueId(),
                code: '',
                opinionCode: `defaultCode_${+new Date()}`,
                order: 1,
                name: '',
                personCode: '',
                proposePosition: '',
                proposedRemovalPosition: '',
                currentPosition: '',
                currentPositionTime: '',
                currentPositionLevelTime: '',
                A0104: '',
                A0117A: '',
                A0107: '',
                A0111A: '',
                feedbackOpinion: '',
                remarks: '',
              },
            ],
          },
        ]);
      }
    } catch (error) {
      console.error('加载沟通表出错:', error);
      message.error('加载沟通表出错，请稍后重试');
      // 设置默认空的沟通表
      setCommunicateTables([
        {
          code: `defaultCode_${+new Date()}`,
          cityLeader: '',
          time: '',
          typeCode: '',
          list: [
            {
              id: generateUniqueId(),
              code: `defaultCode_${+new Date()}`,
              opinionCode: '',
              order: 1,
              name: '',
              personCode: '',
              proposePosition: '',
              proposedRemovalPosition: '',
              currentPosition: '',
              currentPositionTime: '',
              currentPositionLevelTime: '',
              A0104: '',
              A0117A: '',
              A0107: '',
              A0111A: '',
              feedbackOpinion: '',
              remarks: '',
            },
          ],
        },
      ]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 创建新的沟通表
  const createNewCommunicateTable = async () => {
    // try {
    //   const res = await request('/api/swzzbappoint/communicate/addUpdateCommunicate', {
    //     method: 'POST',
    //     body: {
    //       data: {
    //         planCode: planCode,
    //         list: [
    //           {
    //             communicate: {
    //               planCode: planCode,
    //               time: moment().format('YYYY-MM-DD'),
    //             },
    //             list: [],
    //           },
    //         ],
    //       },
    //     },
    //   });

    //   if (res && res.code === 0) {
    //     message.success('创建沟通表成功');
    //     // 重新加载沟通表数据
    //     loadCommunicateTables(planCode);
    //   } else {
    //     message.error(res?.message || '创建沟通表失败');
    //   }
    // } catch (error) {
    //   console.error('创建沟通表出错:', error);
    //   message.error('创建沟通表出错，请稍后重试');
    // }

    //新增一个空表
    const obj = {
      code: `defaultCode_${+new Date()}`,
      cityLeader: '',
      time: '',
      typeCode: '',
      list: [
        {
          id: generateUniqueId(),
          code: `defaultCode_${+new Date()}`,
          opinionCode: '',
          order: 1,
          name: '',
          personCode: '',
          proposePosition: '',
          proposedRemovalPosition: '',
          currentPosition: '',
          currentPositionTime: '',
          currentPositionLevelTime: '',
          A0104: '',
          A0117A: '',
          A0107: '',
          A0111A: '',
          feedbackOpinion: '',
          remarks: '',
        },
      ],
    };
    setCommunicateTables([...communicateTables, obj]);
  };

  // 处理人员选择变更
  const handlePersonChange = (values: string[], rowId: string, tableIndex: number) => {
    console.log('val===', values);


    const selectedPerson: any[] = values.map((value) =>
      personList.find(person => person.personCode === value)
    )
    console.log('selectedPerson===', selectedPerson);

    if (!selectedPerson) return;

    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    const contentIndex = table.list.findIndex(item => item.id === rowId);
    if (contentIndex === -1) return;


    if (!values || values.length === 0) {
      table.list[contentIndex] = {
        ...table.list[contentIndex],
        name: [],
        personCode: values || [],
        proposePosition: '',
        proposedRemovalPosition: '',
        currentPosition: '',
        currentPositionTime: '',
        currentPositionLevelTime: '',
        A0104: '',
        A0117A: '',
        A0107: '',
        A0111A: '',
        remarks: '',
      };
    } else {
      // 更新行数据
      table.list[contentIndex] = {
        ...table.list[contentIndex],
        name: selectedPerson.map(p => p?.name) || '',
        personCode: values || [],
        proposePosition: selectedPerson[0].proposePosition || '',
        proposedRemovalPosition: selectedPerson[0].proposedRemovalPosition || '',
        currentPosition: selectedPerson[0].currentPosition || '',
        currentPositionTime: selectedPerson[0].currentPositionTime || '',
        currentPositionLevelTime: selectedPerson[0].currentPositionLevelTime || '',
        A0104: selectedPerson[0].A0104 || '',
        A0117A: selectedPerson[0].A0117A || '',
        A0107: selectedPerson[0].A0107 || '',
        A0111A: selectedPerson[0].A0111A || '',
        remarks: selectedPerson[0].remarks || '',
      };
    }


    setCommunicateTables(newTables);
  };

  // 处理程序选择变更
  const handleProcedureChange = (value: any, option: any, rowId: string, tableIndex: number) => {
    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    const contentIndex = table.list.findIndex(item => item.id === rowId);
    if (contentIndex === -1) return;

    // 更新程序字段
    table.list[contentIndex].program = value?.CODE_NAME;
    table.list[contentIndex].programCode = value?.CODE_VALUE || '';

    setCommunicateTables(newTables);
  };

  // 更新人员字段值
  const handleFieldChange = (tableIndex: number, rowId: string, field: string, value: string) => {
    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    const contentIndex = table.list.findIndex(item => item.id === rowId);
    if (contentIndex === -1) return;

    // 更新字段值
    table.list[contentIndex][field] = value;

    setCommunicateTables(newTables);
  };

  // 更新沟通表基本信息
  const handleTableInfoChange = (tableIndex: number, field: string, value: any) => {
    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    // 更新沟通表字段
    table[field] = value;

    setCommunicateTables(newTables);
  };

  // 日期变更处理器
  const handleDateChange = (date: any, tableIndex: number) => {
    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    // 更新时间字段
    table.time = date ? date.format('YYYY-MM-DD') : '';

    setCommunicateTables(newTables);
  };

  // 添加行
  const handleAdd = (tableIndex: number) => {
    const newTables = [...communicateTables];
    const table = newTables[tableIndex];
    if (!table) return;

    const maxOrder = Math.max(...table.list.map(item => item.order || 0), 0);

    // 添加新行
    table.list.push({
      id: generateUniqueId(),
      code: '',
      opinionCode: table.code || '',
      order: maxOrder + 1,
      name: '',
      personCode: '',
      proposePosition: '',
      proposedRemovalPosition: '',
      currentPosition: '',
      currentPositionTime: '',
      currentPositionLevelTime: '',
      A0104: '',
      A0117A: '',
      A0107: '',
      A0111A: '',
      feedbackOpinion: '',
      remarks: '',
    });

    setCommunicateTables(newTables);
  };

  // 删除行
  const handleDelete = (tableIndex: number, rowId: string) => {
    const table = communicateTables[tableIndex];
    if (!table || table.list.length <= 1) {
      message.warning('至少保留一行数据');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此条记录吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        const newTables = [...communicateTables];
        const table = newTables[tableIndex];
        if (!table) return;

        // 删除行
        table.list = table.list.filter(item => item.id !== rowId);

        // 更新序号
        table.list = table.list.map((item, index) => ({
          ...item,
          order: index + 1,
        }));

        setCommunicateTables(newTables);
        message.success('删除成功');
      },
    });
  };
  // 删除整张表
  const handleDelTable = (code: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此表吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        let newTables = [...communicateTables];
        // 删除行
        newTables = newTables.filter(item => item.code !== code);
        setCommunicateTables(newTables);
        message.success('删除成功');
      },
    });
  };
  // 保存沟通表
  const saveCommunicateTable = async () => {
    if (!planCode) {
      message.error('方案编码不存在');
      return;
    }

    try {
      // 准备提交的数据格式
      const requestData = {
        data: {
          planCode: planCode,
          tableType: pageType === 'cityLeader' ? 1 : 2, //1-市领导意见 2-市委委员意见
          list: communicateTables.map(table => {
            let type: string | undefined = undefined;
            if (table.typeCode) {
              switch (table.typeCode) {
                case '1':
                  type = '电话沟通';
                  break;
                case '2':
                  type = '书面沟通';
                  break;
                case '3':
                  type = '其他沟通';
                  break;
              }
            }
            return {
              opinionDto: {
                code: table.code.startsWith('defaultCode_') ? undefined : table.code,
                planCode: planCode,
                cityLeader: table.cityLeader || '',
                time: table.time ? moment(table.time).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD'),
                typeCode: table.typeCode || '',
                type: type,
              },
              list: table.list.map(item => ({
                code: item.code?.startsWith('defaultCode_') ? undefined : item.code || undefined,
                opinionCode: item.opinionCode?.startsWith('defaultCode_') ? undefined : item.opinionCode || undefined,
                isDelete: 0,
                name: Array.isArray(item?.name)
                  ? item.name.join(',')
                  : (item?.name || ''),
                personCode: Array.isArray(item?.personCode)
                  ? item.personCode.join(',')
                  : (item?.personCode || ''),
                proposePosition: item.proposePosition || '',
                proposedRemovalPosition: item.proposedRemovalPosition || '',
                currentPosition: item.currentPosition || '',
                currentPositionTime: item.currentPositionTime || '',
                currentPositionLevelTime: item.currentPositionLevelTime || '',
                A0104: item.A0104 || '',
                A0117A: item.A0117A || '',
                A0107: item.A0107 || '',
                A0111A: item.A0111A || '',
                feedbackOpinion: item.feedbackOpinion || '',
                remarks: item.remarks || '',
              })),
            };
          }),
        },
      };
      // 发送请求保存所有沟通表
      const res = await request('/api/swzzbappoint/opinion/addUpdateOpinion', {
        method: 'POST',
        body: requestData,
      });

      if (res && res.code === 0) {
        message.success('保存沟通表成功');
        // 重新加载沟通表数据
        loadCommunicateTables(planCode);
      } else {
        message.error(res?.message || '保存沟通表失败');
      }
    } catch (error) {
      console.error('保存沟通表出错:', error);
      message.error('保存沟通表出错，请稍后重试');
    }
  };

  // 计算不重复人员总数
  const calculateTotalPersons = () => {
    const uniquePersons = new Set();
    communicateTables.forEach(table => {
      table.list.forEach(item => {
        if (item.name) {
          uniquePersons.add(item.name);
        }
      });
    });
    return uniquePersons.size;
  };

  // 渲染沟通表
  const renderCommunicateTables = () => {
    return communicateTables.map((table, tableIndex) => {
      // 计算当前沟通表的人数
      const tablePersonCount = table.list.filter(item => item.name).length;

      return (
        <div key={table.code || tableIndex} className={styles.tableWrapper}>
          <div className={styles.tableHeader}>
            <div className={styles.tableTitle}>
              <h2 style={{ fontSize: '26px' }}>
                {/* 推荐考察前与{pageType === 'cityLeader' ? '市领导' : '市委委员'}沟通情况表 */}
                {pageType === 'cityLeader' ? '市常委会前征求市领导意见人员名单' : '征求市委委员意见人员名单'}
                <Button disabled={communicateTables.length <= 1} type="text" danger title="删除" style={{ marginLeft: '20px' }} onClick={() => handleDelTable(table.code)}>
                  <DeleteOutlined />
                </Button>
              </h2>
              <p style={{ fontSize: '18px' }}>
                （共<span className={styles.xlmzt}>{tablePersonCount}</span>人）
              </p>
            </div>

            <div className={styles.filterRow}>
              <div className={styles.filterItem}>
                <span>市领导：</span>
                <Input style={{ width: 180 }} placeholder="请输入市领导" value={table.cityLeader} onChange={e => handleTableInfoChange(tableIndex, 'cityLeader', e.target.value)} />
              </div>
              <div className={styles.filterItem}>
                <span>时间：</span>
                <TimePicker regNowDate={false} style={{ width: 200 }} value={table.time ? moment(table.time) : null} onChange={date => handleDateChange(date, tableIndex)} format="YYYY-MM-DD" />
              </div>
              <div className={styles.filterItem}>
                <span style={{ whiteSpace: 'nowrap' }}>方式：</span>

                <div style={{ width: 150 }}>
                  {/* @ts-ignore */}
                  <DictSelect placeholder="请选择方式" codeType="SWZZBAPPIONT_TALKWAY" initValue={table.typeCode} onChange={value => handleTableInfoChange(tableIndex, 'typeCode', value)} />
                </div>
              </div>
            </div>
          </div>

          <table className={styles.seekTable}>
            <colgroup>
              {/* 序号 */}
              <col style={{ width: '60px' }} />
              {/* 姓名 */}
              <col style={{ width: '120px' }} />
              {/* 拟任职务 */}
              <col style={{ width: '200px' }} />
              {/* 拟免职务 */}
              <col style={{ width: '200px' }} />
              {/* 现任职务 */}
              <col style={{ width: '200px' }} />
              {/* 任现职务时间 */}
              <col style={{ width: '100px' }} />
              {/* 任现职级时间 */}
              <col style={{ width: '100px' }} />
              {/* 性别 */}
              <col style={{ width: '60px' }} />
              {/* 民族 */}
              <col style={{ width: '70px' }} />
              {/* 出生年月 */}
              <col style={{ width: '100px' }} />
              {/* 籍贯 */}
              <col style={{ width: '100px' }} />
              {/* 反馈意见 */}
              <col style={{ width: '150px' }} />
              {/* 文件上传 */}
              <col style={{ width: '150px' }} />
              {/* 备注 */}
              <col style={{ width: '150px' }} />
              {/* 操作 */}
              <col style={{ width: '100px' }} />
            </colgroup>
            <thead>
              <tr className={styles.fzfsfm}>
                <th>序号</th>
                <th>姓名</th>
                <th>拟任职务</th>
                <th>拟免职务</th>
                <th>现任职务</th>
                <th>任现职务时间</th>
                <th>任现职级时间</th>
                <th>性别</th>
                <th>民族</th>
                <th>出生年月</th>
                <th>籍贯</th>
                <th>反馈意见</th>
                <th>文件上传</th>
                <th>备注</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {table.list.map((row, rowIndex) =>
              (
                <tr key={row.id} className={styles.fzfsfm}>
                  <td>{row.order}</td>
                  <td>
                    <Select
                      mode="multiple"
                      maxTagCount={3}
                      showSearch
                      style={{ width: '100%' }}
                      placeholder="请选择人员"
                      optionFilterProp="children"
                      value={Array.isArray(row.personCode) ? row.personCode : (row.personCode ? [row.personCode] : [])}
                      onChange={value => handlePersonChange(value, row.id!, tableIndex)}
                      filterOption={(input, option) => option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    >
                      {/* {personList.map((person, index) => (
                        <Option key={person.code} value={person.code || ''}>
                          {person.name}
                        </Option>
                      ))} */}
                      {personList.filter((person) => {
                        // 获取当前表中其他行已选择的人员
                        const selectedPersons = table.list
                          .filter(item => item.code !== row.code)
                          .flatMap(item => item.personCode || []);

                        // 返回未被其他行选择的人员
                        return !selectedPersons.includes(person.personCode);
                      }).map(person => {
                        return (
                          <Option
                            key={person.personCode}
                            value={person.personCode}
                          >
                            {person.name}
                          </Option>
                        )
                      })}
                    </Select>
                  </td>
                  <td style={{ textAlign: 'left' }}>
                    {row.proposePosition}
                    {/* <Input value={row.jobChange} onChange={e => handleFieldChange(tableIndex, row.id!, 'jobChange', e.target.value)} placeholder="请输入职级变动" /> */}
                  </td>
                  <td style={{ textAlign: 'left' }}>
                    {row.proposedRemovalPosition}
                    {/* <Input value={row.proposePosition} onChange={e => handleFieldChange(tableIndex, row.id!, 'proposePosition', e.target.value)} placeholder="请输入拟任职务" /> */}
                  </td>
                  <td style={{ textAlign: 'left' }}>
                    {row.currentPosition}
                    {/* <Input value={row.proposedRemovalPosition} onChange={e => handleFieldChange(tableIndex, row.id!, 'proposedRemovalPosition', e.target.value)} placeholder="请输入拟免职务" /> */}
                  </td>
                  <td>
                    {row.currentPositionTime}
                    {/* <Input value={row.currentPosition} onChange={e => handleFieldChange(tableIndex, row.id!, 'currentPosition', e.target.value)} placeholder="请输入现任职务" /> */}
                  </td>
                  <td>{row.currentPositionLevelTime || ''}</td>
                  <td>{row.A0104 ? (row.A0104 === '1' ? '男' : '女') : ''}</td>
                  <td>{row.A0117A || ''}</td>
                  <td style={{ whiteSpace: 'pre-wrap' }}>{row.A0107 || ''}</td>
                  <td>{row.A0111A || ''}</td>
                  <td>
                    <Input.TextArea value={row.feedbackOpinion} onChange={e => handleFieldChange(tableIndex, row.id!, 'feedbackOpinion', e.target.value)} placeholder="请输入反馈意见" />
                  </td>
                  <td>
                    <Upload
                      headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                      action="/api/base/upload"
                      showUploadList={false}
                      multiple={false}
                      onChange={(info) => handleListUploadChange(info, row.id)}
                      accept=".docx,.doc.wps"
                    >
                      <Button loading={importLoading}>
                        导入文件
                      </Button>
                    </Upload>
                    <div style={{
                      // width: '100%',
                      wordWrap: 'break-word',
                      wordBreak: 'break-all',      // 允许在单词内换行
                      whiteSpace: 'normal',
                      overflowWrap: 'break-word',
                      color: '#36d',
                      cursor: 'pointer',
                      marginLeft: "165px"
                    }} onClick={(e) => {
                      e.stopPropagation()
                      fileDownload(row?.url, row?.url?.split('\\').pop())
                    }
                    }>{row?.url ? row?.url.split('\\').pop() : ''}</div>
                  </td>
                  <td>
                    <Input.TextArea value={row.remarks} onChange={e => handleFieldChange(tableIndex, row.id!, 'remarks', e.target.value)} placeholder="请输入备注" />
                  </td>
                  <td>
                    <Space>
                      <Button type="text" danger icon={<DeleteOutlined />} title="删除" onClick={() => handleDelete(tableIndex, row.id!)} disabled={table.list.length <= 1} />
                      <Button type="text" icon={<PlusOutlined />} title="添加" onClick={() => handleAdd(tableIndex)} />
                    </Space>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {tableIndex < communicateTables.length - 1 && <Divider />}
        </div>
      );
    });
  };
  // 导出
  const handleExport = useCallback(async () => {
    setExportLoading(true);
    try {
      const res = await request('/api/swzzbappoint/communicate/exportCommunicate', {
        method: 'POST',
        body: {
          data: {
            planCode: planCode,
            tableType: pageType === 'cityLeader' ? 1 : 2, //1-市领导意见 2-市委委员意见
          },
        },
      });
      if (res && res.code === 0) {
        fileDownload(`/api${res.data}`);
      }
    } catch (error) {
      message.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      setExportLoading(false);
    }
  }, [planCode]);
  // 预览功能
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewVisible(false);
  };

  // 处理文件上传前的检查
  const beforeUpload = file => {
    // 检查文件大小
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过2MB!');
      return Upload.LIST_IGNORE;
    }

    setImportLoading(true);
    return true;
  };
  // 导入文件
  const importFile = async file => {
    console.log('导入文件:', file);
    if (file.url) {
      setFileUrl(file.url);
      try {
        const importRes = await request(`/api/swzzbappoint/examinationMaterials/uploadArchiveMaterials?id=${querys?.id}&url=${file.url}`, {
          method: 'GET',
        });

        if (importRes && importRes.code === 0) {
          message.success('导入成功');
          // 导入成功后页面显示只导入的内容，不再显示原来的页面
          setHtmlContent(importRes.data?.archiveMaterialsFile || '');
          setFileUrl(importRes.data?.fileUrl || '');
          sessionStorage.setItem('archiveMaterialsFile', importRes.data?.archiveMaterialsFile || ''); // 保存html内容到sessionStorage
          // 刷新综合任免卷 页面
          localStorage.setItem('archiveMaterialsPageKey', `${+new Date()}`);
          // 更新路由上的fileUrl参数
          const query = { ..._history.location.query, fileUrl: importRes.data?.fileUrl || '' };
          _history.replace({
            pathname: _history.location.pathname,
            query,
          });
        } else {
          message.error(importRes.message || '导入失败');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请重试');
      } finally {
        setImportLoading(false);
      }
    }
  };
  // 处理文件上传变更
  const handleUploadChange = info => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFile(file);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  };

  // 处理列表文件上传变更
  const handleListUploadChange = (info, id) => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];
        setCommunicateTables((pevData) => {
          debugger
          return pevData.list.map((item) => {
            if (item.id == id) {
              return {
                ...item,
                url: file.url
              }
            }
            return item
          })
        })
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  };
  // 导出文件
  const doDownload = () => {
    if (fileUrl) {
      fileDownload(`/api${fileUrl}`);
    }
  };

  // 渲染预览内容
  const renderPreviewContent = () => {
    return (
      <div className={styles.previewContainer}>
        {communicateTables.map((table, tableIndex) => {
          // 计算当前沟通表的人数
          const tablePersonCount = table.list.filter(item => item.name).length;

          return (
            <div key={table.code || tableIndex} className={styles.tableWrapper}>
              <div className={styles.tableHeader}>
                <div className={styles.tableTitle}>
                  <h2> 推荐考察前与{pageType === 'cityLeader' ? '市领导' : '市委委员'}沟通情况表</h2>
                  <p>（共{tablePersonCount}人）</p>
                </div>

                <div className={styles.filterRow}>
                  <div className={styles.filterItem}>
                    <span>市领导：</span>
                    <span className={styles.previewValue}>{table.cityLeader || '无'}</span>
                  </div>
                  <div className={styles.filterItem}>
                    <span>时间：</span>
                    <span className={styles.previewValue}>{table.time || '无'}</span>
                  </div>
                  <div className={styles.filterItem}>
                    <span style={{ whiteSpace: 'nowrap' }}>方式：</span>
                    <div style={{ width: 150 }}>
                      {/* @ts-ignore */}
                      <DictSelect placeholder="请选择方式" codeType="SWZZBAPPIONT_TALKWAY" initValue={table.typeCode} disabled={true} />
                    </div>
                  </div>
                </div>
              </div>

              <table className={styles.seekTable}>
                <colgroup>
                  {/* 序号 */}
                  <col style={{ width: '60px' }} />
                  {/* 姓名 */}
                  <col style={{ width: '100px' }} />
                  {/* 拟任职务 */}
                  <col style={{ width: '200px' }} />
                  {/* 拟免职务 */}
                  <col style={{ width: '200px' }} />
                  {/* 现任职务 */}
                  <col style={{ width: '200px' }} />
                  {/* 任现职务时间 */}
                  <col style={{ width: '100px' }} />
                  {/* 任现职级时间 */}
                  <col style={{ width: '100px' }} />
                  {/* 性别 */}
                  <col style={{ width: '60px' }} />
                  {/* 民族 */}
                  <col style={{ width: '70px' }} />
                  {/* 出生年月 */}
                  <col style={{ width: '100px' }} />
                  {/* 籍贯 */}
                  <col style={{ width: '100px' }} />
                  {/* 反馈意见 */}
                  <col style={{ width: '150px' }} />
                  {/* 文件上传 */}
                  <col style={{ width: '150px' }} />
                  {/* 备注 */}
                  <col style={{ width: '150px' }} />
                </colgroup>
                <thead>
                  <tr className={styles.fzfsfm}>
                    <th>序号</th>
                    <th>姓名</th>
                    <th>拟任职务</th>
                    <th>拟免职务</th>
                    <th>现任职务</th>
                    <th>任现职务时间</th>
                    <th>任现职级时间</th>
                    <th>性别</th>
                    <th>民族</th>
                    <th>出生年月</th>
                    <th>籍贯</th>
                    <th>反馈意见</th>
                    <th>文件上传</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  {table.list.map((row, rowIndex) => (
                    <tr key={row.id} className={styles.fzfsfm}>
                      <td>{row.order}</td>
                      <td>{row.name || ''}</td>
                      <td style={{ textAlign: 'left' }}>{row.proposePosition || ''}</td>
                      <td style={{ textAlign: 'left' }}>{row.proposedRemovalPosition || ''}</td>
                      <td style={{ textAlign: 'left' }}>{row.currentPosition || ''}</td>
                      <td>{row.currentPositionTime || ''}</td>
                      <td>{row.currentPositionLevelTime || ''}</td>
                      <td>{row.A0104 ? (row.A0104 === '1' ? '男' : '女') : ''}</td>
                      <td>{row.A0117A || ''}</td>
                      <td style={{ whiteSpace: 'pre-wrap' }}>{row.A0107 || ''}</td>
                      <td>{row.A0111A || ''}</td>
                      <td>{row.feedbackOpinion || ''}</td>
                      <td>{row.remarks || ''}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {tableIndex < communicateTables.length - 1 && <Divider />}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.container}>
        {querys?.comprehensive !== 'true' && (
          <div className={styles.topButtons}>
            <Button type="primary" onClick={createNewCommunicateTable}>
              新增沟通表
            </Button>
            <Button type="primary" onClick={saveCommunicateTable}>
              保存
            </Button>
            <Button type="primary" onClick={handleExport} loading={exportLoading}>
              导出
            </Button>
            <Button type="primary" onClick={handlePreview}>
              预览
            </Button>
          </div>
        )}
        {querys?.comprehensive === 'true' && (
          <div className={styles.topButtons}>
            <Upload
              headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
              action="/api/base/upload"
              beforeUpload={beforeUpload}
              onChange={handleUploadChange}
              showUploadList={false}
              accept=".docx,.doc"
            >
              <Button type="primary" loading={importLoading}>
                导入文件
              </Button>
            </Upload>
            <Button type="primary" onClick={fileUrl ? doDownload : handleExport}>
              导出文件
            </Button>
          </div>
        )}
        {/* 如果导入文件，页面将只显示导入文件的内容，不再显示原来的内容 */}
        {htmlContent ? <div dangerouslySetInnerHTML={{ __html: htmlContent }} /> : <div className={styles.tablesContainer}>{renderCommunicateTables()}</div>}

        {/* 预览模态窗口 */}
        <Modal
          title="沟通表预览"
          visible={previewVisible}
          onCancel={handleClosePreview}
          footer={[
            <Button key="close" type="primary" onClick={handleClosePreview}>
              关闭
            </Button>,
          ]}
          width={1500}
          bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
        >
          {renderPreviewContent()}
        </Modal>
      </div>
    </Spin>
  );
};

export default SeekPage;
