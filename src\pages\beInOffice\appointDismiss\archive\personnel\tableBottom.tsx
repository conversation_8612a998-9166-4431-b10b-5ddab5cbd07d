import React, { useEffect, useMemo, useState } from 'react';
import styles from '@/components/Appoint/table/index.less';
import EditDiv from './components/edit'
import DictSelect from '@/components/DictSelect';
import TimePicker from '@/components/TimePicker';
import Assess from '@/components/Appoint/components/assess';
import { Input, Modal } from 'antd';
import RewardPunishment from '@/components/Appoint/components/rewardpunishment';
const TextArea = Input.TextArea;
//任免表基础信息
export default function BasisBottom(props) {
  const [assess, setAssess] = useState(false);
  const [reward, setReward] = useState(false);
  const { theme, setTheme, getBasicInfo, basicInfoData, pointerEvents, isEdit, redact, editPermissions = {}, isReadOnly = false, showFamilyClass = true } = props;
  useEffect(() => {
    //请求字典表
    const dict = ['GB4761', 'GB4762'];
    for (let obj of dict) {
      props.dispatch({
        type: 'commonDict/getDict',
        payload: {
          data: {
            codeType: obj,
          },
        },
      });
    }
  }, []);
  function a36VoListChange(value, key, index) {
    // console.log(value, key, index, theme, '--------------------------------------')
    if (pointerEvents === 'none' || isReadOnly) {
      return;
    }
    if (theme['a36VoList']) {
      const a36VoList = [...theme.a36VoList];
      if (key === 'a3604A') {
        if (theme['a0104'] === '1' && value === '丈夫') {
          // 男
          return false;
        }
        if (theme['a0104'] === '2' && value === '妻子') {
          // 男
          return false;
        }
      }
      a36VoList[index][key] = value;
      theme.a36VoList = a36VoList;
      setTheme({ ...theme });
    }
  }
  function handleCancel() {
    setAssess(false);
    setReward(false);
    getBasicInfo();
  }
  function insertTheme(key, val) {
    console.log('insertTheme', val)
    setTheme(old => {
      return { ...old, [key]: val };
    });
  }
  //rewardEdit 奖惩信息 assessEdit 年度考核 familyEdit 家庭成员
  let rewardEdit = {},
    assessEdit = {},
    familyEdit = {};
  //编辑模式且有信息项公开
  if (isEdit && redact) {
    if (editPermissions['8']) {
      rewardEdit = { pointerEvents: 'auto' };
    }
    if (editPermissions['9']) {
      assessEdit = { pointerEvents: 'auto' };
    }
    if (editPermissions['10']) {
      familyEdit = { pointerEvents: 'auto' };
    }
  }
  return (
    <React.Fragment>
      {console.log('tableBottom.tsx', new Date().valueOf())}
      <Modal maskClosable={false} title={'年度考核结果'} wrapClassName={styles.modalInfo} visible={assess} width={1200} footer={null} onOk={handleCancel} onCancel={handleCancel} destroyOnClose>
        {assess && <Assess location={props.location} theme={theme} />}
      </Modal>
      <Modal maskClosable={false} title={'奖惩情况'} wrapClassName={`${styles.modalInfo} ${styles.rewardModal}`} visible={reward} width={1200} footer={null} onCancel={handleCancel} destroyOnClose>
        {reward && <RewardPunishment infoSet={{}} {...props} getBasicInfo={getBasicInfo} basicInfo={basicInfoData || {}} />}
      </Modal>
      <table className={`${styles.appoint} ${styles.appoint3}`} style={{ pointerEvents }}>
        <tbody>
          <tr className={styles.situation} style={rewardEdit}>
            <td className={styles.fWidth} style={{ width: 63 }}>
              奖<br />惩<br />情<br />况
            </td>
            <td className={styles.resume} colSpan={5}
              // onClick={() => !isReadOnly && setReward(true)} 
              style={{ padding: 0 }}>
              {/* <div className={styles.showLeft}>{theme['a14Z101'] || ''}</div> */}
              {/* <div className={styles.textArea}></div> */}
              {/* <TextArea value={theme['a14Z101'] || ''} autoSize={true} /> */}
              {/* <TextArea style={{ fontSize: '15px' }} value={theme['a14Z101'] || ''} autoSize={true} onChange={(e) => insertTheme('a14Z101', e.target.value)} /> */}
              <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt !important' }} unKey={'a14Z101'} value={theme['a14Z101'] || ''} change={(e) => insertTheme('a14Z101', e)} />
            </td>
          </tr>
          <tr className={styles.situation} style={assessEdit}>
            <td className={styles.fWidth} style={{ width: 63 }}>
              年核
              <br />
              度结
              <br />
              考果
            </td>
            <td className={styles.resume} colSpan={5}
            // onClick={() => !isReadOnly && setAssess(true)}
            >
              {/* <div className={styles.showLeft}>{theme['a15Z101'] || ''}</div> */}
              <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt' }} unKey={'a15Z101'} value={theme['a15Z101'] || ''} change={(e) => insertTheme('a15Z101', e)} />
            </td>
          </tr>
          <tr className={styles.situation}>
            <td className={styles.fWidth} style={{ width: 63 }}>
              任<br />免<br />理<br />由
            </td>
            <td colSpan={5} className={`${styles.resume} ${styles.showLeft} ${styles.alwaysLeft}`}>
              {/* <EditDiv unKey={'rmly'} value={theme['rmly'] || ''} /> */}
              <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt' }} unKey={'rmly'} value={theme['rmly'] || ''} change={(e) => insertTheme('rmly', e)} />
            </td>
          </tr>

          <tr className={showFamilyClass ? styles.family : ''}>
            <td rowSpan={11} style={{ width: 63 }}>
              家<br />庭<br />主<br />要<br />成<br />员<br />及<br />重<br />要<br />社<br />会<br />关<br />系
            </td>
            <td style={{ width: 73 }}>称&emsp;谓</td>
            <td style={{ width: 101 }}>姓&emsp;名</td>
            <td style={{ width: 101 }}>出生日期</td>
            <td style={{ width: 88 }}>政治面貌</td>
            <td style={{ width: 223 }}>工 作 单 位 及 职 务</td>
          </tr>
          {(function (func) {
            let data: any = [];
            for (let i = 0; i < 10; i++) {
              let obj = {};
              if (theme['a36VoList'] && theme['a36VoList'].length > 0) {
                obj = theme['a36VoList'][i] || {};
              }
              data.push(
                <tr key={obj['A3600'] || i} style={familyEdit}>
                  <td className={`${styles.noBC} ${styles.selectWidth}`} style={{ width: 70 }}>
                    <DictSelect
                      placeholder={''}
                      extendProps={{ dropdownMatchSelectWidth: false }}
                      codeType={'GB4761'}
                      send={false}
                      initValue={obj['a3604A'] || undefined}
                      onChange={e => func(e, 'a3604A', i)}
                    />
                  </td>
                  <td style={{ width: 101 }}>
                    <EditDiv change={e => func(e, 'a3601', i)} unKey={obj['A3600'] + obj['a3601']} maxLen={5} value={obj['a3601']} />
                  </td>
                  <td style={{ width: 101 }} className={styles.inputTime}>
                    <TimePicker regNowDate={false} key={i} mode={'month'} format={'YYYY.MM'} value={obj['a3607']} placeholder={''} onBlur={v => func(v, 'a3607', i)} allowClear={true} />
                  </td>
                  <td style={{ width: 88 }} className={styles.selectWidth}>
                    <DictSelect
                      placeholder={''}
                      extendProps={{ dropdownMatchSelectWidth: false }}
                      codeType={'GB4762'}
                      send={false}
                      initValue={obj['a3627'] || undefined}
                      onChange={e => func(e, 'a3627', i)}
                    />
                  </td>
                  <td style={{ width: 223, textAlign: 'left' }} className={styles.alwaysLeft}>
                    <EditDiv change={e => func(e, 'a3611', i)} unKey={obj['A3600'] + obj['a3611']} maxLen={100} value={obj['a3611']} />
                  </td>
                </tr>
              );
            }
            return data;
          })(a36VoListChange)}
        </tbody>
      </table>
    </React.Fragment>
  );
}
