import React, { Fragment, useEffect, useMemo, useState } from 'react';
import styles from '@/components/Appoint/table/index.less';
import EditDiv from './components/edit';
import { FileSyncOutlined } from '@ant-design/icons';
import { Input, Modal, Select } from 'antd';
import moment from 'moment';
import DictModalTree from '@/components/DictModalTree';
import JoinOrgDataComp from '@/components/Appoint/components/joinOrgDataComp';
import DictSelect from '@/components/DictSelect';
import AdaptiveTextDiv from '@/components/AdaptiveTextDiv';
import { a1701Format, resumeSet, toLowerCase } from '@/utils/method';
import Resume from '@/components/Appoint/table/resume';
import Position from '@/components/Appoint/components/position';
import Education from '@/components/Appoint/components/education';
import TechnicalPosition from '@/components/Appoint/components/technicalposition';
import { error } from '@/components/Notice';
import Photo from '@/components/Appoint/components/photo';
import { _history } from '@/utils/session';
import { getSession } from '@/utils/session';

const format = 'YYYY.MM';
function addString(str) {
  if (!str) {
    return '';
  }
  let arr = str.split('');
  arr.splice(4, 0, '.');
  return arr.join('');
}

//任免表基础信息
export default function Basis(props) {
  const [position, setPosition] = useState(false);
  const [education, setEducation] = useState(false);
  const [visible, setVisible] = useState(false);
  const [photo, setPhoto] = useState(false);
  const { theme, setTheme, basicInfoData, GB4762List, getBasicInfo, pointerEvents, isEdit, redact, editPermissions = {} } = props;

  const { pathname } = _history.location;
  useEffect(() => {
    resumeSet(basicInfoData['a1701'] || '', props['id']);
  }, [basicInfoData]);
  function A0104Change(v) {
    insertTheme('a0104', v);
  }
  // 民族
  function a0117Change(val) {
    insertTheme('a0117', val.CODE_VALUE);
  }
  // 生日
  function birthChange(val) {
    insertTheme('a0107', val);
  }
  function birthCheck() {
    const { a0144, a0134, a0107 } = theme;

    if (a0144 && a0107 && moment(a0144, format).endOf('day') < moment(a0107, format).endOf('day')) {
      return error('出生时间不能晚于入党时间');
    }
    if (a0134 && a0107 && moment(a0134, format).endOf('day') < moment(a0107, format).endOf('day')) {
      theme['a0134'] = '';
      return error('出生日期不能晚于参加工作时间');
    }
  }
  function workChange(val) {
    insertTheme('a0134', val);
  }
  function insertTheme(key, val) {
    setTheme(old => {
      return { ...old, [key]: val };
    });
  }
  // 入党时间
  function getJoinOrgDateInfo(val) {
    const { A0144 } = val;
    if (A0144 && theme['a0107'] && moment(A0144, format).endOf('day') < moment(theme['a0107'], format).endOf('day')) {
      return error('入党时间不能早于出生时间');
    }
    setTheme(old => {
      return { ...old, ...toLowerCase(val) };
    });
  }

  function handleCancel() {
    setPosition(false);
    setEducation(false);
    setVisible(false);
    setPhoto(false);
    getBasicInfo && getBasicInfo();
  }
  function formatTime(key) {
    let str = theme[key];
    let reg = /(\d{4}\.(0[0-9]|1[0-2])|\d{4}(0[0-9]|1[0-2]))/;
    if (reg.test(str)) {
      if (str.includes('.')) {
        str = str.substr(0, 7);
      } else {
        str = addString(str.substr(0, 6));
      }
      setTheme(() => {
        return { ...theme, [key]: str };
      });
    } else {
      setTheme(() => {
        return { ...theme, [key]: '' };
      });
      error('时间格式错误');
    }
  }
  //basicEdit基础信息 photoEdit照片 dutyEdit现任职务 majorEdit专业技术 eduEdit学历学位
  let basicEdit = {},
    photoEdit = {},
    dutyEdit = {},
    majorEdit = {},
    eduEdit = {};
  //编辑模式且有信息项公开
  if (isEdit) {
    if (redact) {
      //有信息项公开 判断基础信息是否公开
      if (editPermissions['1']) {
        basicEdit = { pointerEvents: 'auto' };
      }
      if (editPermissions['2']) {
        photoEdit = { pointerEvents: 'auto' };
      }
      if (editPermissions['3']) {
        dutyEdit = { pointerEvents: 'auto' };
      }
      if (editPermissions['6']) {
        majorEdit = { pointerEvents: 'auto' };
      }
      if (editPermissions['7']) {
        eduEdit = { pointerEvents: 'auto' };
      }
    }
  } else {
    // 新增信息项控制
    basicEdit = { pointerEvents: 'auto' };
    photoEdit = { pointerEvents: 'auto' };
  }
  return (
    <React.Fragment>
      {console.log('retur', theme, new Date().toLocaleString())}
      <Modal
        maskClosable={false}
        title={`${pathname}`.startsWith('/career') ? '现任职务（岗位）' : `${pathname}`.startsWith('/stateOwned') ? '现任职务' : '现任职务（职级）'}
        visible={position}
        width={1200}
        footer={null}
        onCancel={handleCancel}
        destroyOnClose
      >
        {position && <Position getBasicInfo={getBasicInfo} basicInfo={basicInfoData || {}} />}
      </Modal>

      <Modal maskClosable={false} title={'学历学位'} wrapClassName={`${styles.modalInfo} ${styles.eduModal}`} visible={education} width={1200} footer={null} onCancel={handleCancel} destroyOnClose>
        {education && <Education />}
      </Modal>

      <Modal
        maskClosable={false}
        title={`${pathname}`.startsWith('/career') ? '专业技术资格' : `${pathname}`.startsWith('/stateOwned') ? '专业技术职业资格' : '专业技术职务'}
        wrapClassName={`${styles.modalInfo} ${styles.tecModal}`}
        visible={visible}
        width={1200}
        footer={null}
        onCancel={handleCancel}
        destroyOnClose
      >
        {visible && <TechnicalPosition getBasicInfo={getBasicInfo} basicInfo={basicInfoData || {}} />}
      </Modal>

      <Modal maskClosable={false} title={'多媒体'} wrapClassName={styles.modalInfo} visible={photo} width={900} footer={null} onCancel={handleCancel} destroyOnClose>
        {photo && <Photo getBasicInfo={getBasicInfo} {...props} />}
      </Modal>

      <table className={styles.appoint} style={{ pointerEvents }} cellPadding={0}>
        <tbody>
          <tr style={basicEdit}>
            <td className={styles.fWidth}>姓&emsp;名</td>
            <td className={styles.fCWidth}>
              <EditDiv unKey={'a0101'} maxLen={18} value={theme['a0101'] || ''} change={e => insertTheme('a0101', e)} />
            </td>
            <td className={styles.fWidth}>性&emsp;别</td>
            <td className={`${styles.fCWidth} ${styles.selectWidth}`}>
              <Select allowClear dropdownMatchSelectWidth={false} onChange={A0104Change} value={theme['a0104']}>
                <Select.Option title={'男'} value={'1'}>
                  男
                </Select.Option>
                <Select.Option title={'女'} value={'2'}>
                  女
                </Select.Option>
              </Select>
            </td>
            <td className={styles.secWidth}>
              出生年月
              <br />
              （岁）
            </td>
            <td className={`${styles.secCWidth} ${styles.inputTime}`}>
              <div style={{ height: '100%' }}>
                <Input
                  id={'birthday'}
                  value={theme['a0107']}
                  allowClear
                  onChange={e => {
                    e.persist();
                    birthChange(e.target.value);
                  }}
                  onBlur={e => {
                    e.persist();
                    birthCheck();
                    formatTime('a0107');
                  }}
                />
                {// @ts-ignore
                  'birthday' !== document.activeElement.id && theme['a0107'] && (
                    <Fragment>
                      （
                      {moment()
                        .diff(moment(theme['a0107'], format), 'years')
                        .toString() !== 'NaN'
                        ? moment().diff(moment(theme['a0107'], format), 'years')
                        : ''}
                      岁）
                    </Fragment>
                  )}
              </div>
            </td>
            <td
              className={styles.headWidth}
              style={{ ...photoEdit, padding: 0 }}
              rowSpan={4}
              onClick={() => {
                setPhoto(true);
              }}
            >
              {basicInfoData['a0198'] ? <img style={{ width: 150, height: 206 }} src={`/api${basicInfoData['a0198']}`} alt="avatar" /> : '无照片'}
            </td>
          </tr>
          <tr style={basicEdit}>
            <td>民&emsp;族</td>
            <td className={styles.fCWidth}>
              <DictModalTree codeType={'GB3304'} title={'民族'} parentDisable={true} onChange={a0117Change} initValue={theme['a0117'] || undefined} placeholder={''} />
            </td>
            <td>籍&emsp;贯</td>
            <td className={styles.fCWidth}>
              <EditDiv unKey={'a0111A'} value={theme['a0111A'] || ''} change={e => insertTheme('a0111A', e)} />
            </td>
            <td>出&ensp;生&ensp;地</td>
            <td className={styles.secCWidth}>
              <EditDiv unKey={'a0114A'} value={theme['a0114A'] || ''} change={e => insertTheme('a0114A', e)} />
            </td>
          </tr>
          <tr style={basicEdit}>
            <td>
              入&emsp;党
              <br />
              时&emsp;间
            </td>
            {/* <JoinOrgDataComp
              initalData={{ GB4762List }}
              handOk={getJoinOrgDateInfo}
              initialValue={{ a0141: theme['a0141'], a0144: theme['a0144'], a3921: theme['a3921'], a3927: theme['a3927'], a0140: theme['a0140'], a0107: theme['a0107'] }} //编辑的数据要往state里面传
            >
              <td className={styles.fCWidth}>{theme.a0140}</td>
            </JoinOrgDataComp> */}
            <td>
              <Input
                id={'birthday'}
                value={theme['a0144'] ? moment(theme['a0144']).format('YYYY.MM') : ''}
                allowClear
                onChange={e => {
                  e.persist();
                  birthChange(e.target.value);
                }}
                onBlur={e => {
                  e.persist();
                  birthCheck();
                  formatTime('a0140');
                }}
              />
            </td>
            <td>
              参加工
              <br />
              作时间
            </td>
            <td className={`${styles.fCWidth} ${styles.inputTime}`}>
              <Input
                value={theme['a0134']}
                onChange={e => {
                  e.persist();
                  workChange(e.target.value);
                }}
                onBlur={e => {
                  e.persist();
                  let str = e.target.value,
                    format = 'YYYYMMDD';
                  if (str.includes('.')) {
                    format = 'YYYY.MM.DD';
                  }
                  if (str && theme['a0107'] && moment(str, format).endOf('month') <= moment(theme['a0107']).endOf('month')) {
                    workChange(undefined);
                    return error('参加工作时间不能早于出生日期');
                  }
                  if (moment(str, format).endOf('day') > moment().endOf('day')) {
                    workChange(undefined);
                    return error('参加工作时间不能大于当前时间');
                  }
                  formatTime('a0134');
                }}
              />
            </td>

            <td>到龄时间</td>
            <td className={`${styles.secCWidth} ${styles.selectWidth}`}>
              <div style={{ height: '100%' }}>
                <span>{theme['a01Z119']}</span>
                {// @ts-ignore
                  'A01Z119' !== document.activeElement.id && theme['a01Z119'] && (
                    <div style={{ fontSize: 14 }}>
                      （
                      {moment()
                        .diff(moment(theme['a01Z119'], format), 'years')
                        .toString() !== 'NaN'
                        ? (() => {
                          if (!theme['a0107']) {
                            return '';
                          }
                          const a01Z119 = moment(theme['a01Z119'], format);
                          const birthday = moment(theme['a0107'], format);
                          const years = a01Z119.diff(birthday, 'years');
                          const totalMonths = a01Z119.diff(birthday, 'months');
                          const months = totalMonths % 12; // 计算除去整年后的月数
                          return `${years}岁${months == 0 ? '' : months + '个月'}`;
                        })()
                        : ''}
                      ）
                    </div>
                  )}
              </div>
            </td>
          </tr>
          <tr style={majorEdit}>
            <td>
              健&emsp;康 <br />
              状&emsp;况
            </td>
            <td>
              <DictSelect extendProps={{ dropdownMatchSelectWidth: false }} codeType={'GB2261D'} initValue={theme['a0128B']} onChange={i => insertTheme('a0128B', i)} />
            </td>
            {`${pathname}`.startsWith('/career') || `${pathname}`.startsWith('/stateOwned') ? (
              <td>
                专业技
                <br />
                术资格
              </td>
            ) : (
              <td>
                专业技
                <br />
                术职务
              </td>
            )}
            <td
              className={`${styles.fCWidth}`}
              onClick={() => {
                setVisible(true);
              }}
            >
              <p style={{ marginBottom: 0, textAlign: 'left', display: 'inline-block' }}>{theme['a0196'] || ''}</p>
            </td>
            <td>
              熟悉专业
              <br />
              有何专长
            </td>
            <td className={`${styles.secCWidth} ${styles.jk}`}>
              <EditDiv maxLen={56} unKey={'a0187A'} value={theme['a0187A'] || ''} />
            </td>
          </tr>
          {/* <tr style={majorEdit}>
            {`${pathname}`.startsWith('/career') || `${pathname}`.startsWith('/stateOwned') ? (
              <td>
                专业技
                <br />
                术资格
              </td>
            ) : (
              <td>
                专业技
                <br />
                术职务
              </td>
            )}
            <td colSpan={2} style={{ width: 148, textAlign: 'center' }}>
              <EditDiv unKey={'a0196'} value={theme['a0196'] || ''} change={e => insertTheme('a0196', e)} />
            </td>
            <td>
              熟悉专业
              <br />
              有何专长
            </td>
            <td colSpan={2} style={{ width: 201 }}>
              <EditDiv maxLen={56} unKey={'a0187A'} value={theme['a0187A'] || ''} change={e => insertTheme('a0187A', e)} />
            </td>
          </tr> */}
          <tr id={'1'} style={{ ...eduEdit, height: 60 }} onClick={() => { }}>
            <td rowSpan={2}>
              学&emsp;历
              <br />
              学&emsp;位
            </td>
            <td className={styles.BC}>
              全日制
              <br />
              教&emsp;育
            </td>
            <td colSpan={2} className={styles.edu} style={{ width: 160 }}>
              <div style={{ borderBottom: '1px dashed #ccc' }}>
                {/* <AdaptiveTextDiv text={theme['qRZXL']} divHeight={25} /> */}
                <EditDiv maxLen={18} unKey={'qRZXL'} value={theme['qRZXL'] || ''} change={e => insertTheme('qRZXL', e)} />
              </div>
              <div>
                {/* <AdaptiveTextDiv text={theme['qRZXW']} divHeight={25} /> */}
                <EditDiv unKey={'qRZXW'} value={theme['qRZXW'] || ''} change={e => insertTheme('qRZXW', e)} />
              </div>
            </td>
            <td className={styles.BC}>
              毕业院校
              <br />
              系及专业
            </td>
            <td colSpan={2} className={styles.eduPro}>
              <div style={{ borderBottom: '1px dashed #ccc' }}>
                {/* <AdaptiveTextDiv text={theme['qRZXLXX']} divHeight={25} /> */}
                <EditDiv unKey={'qRZXLXX'} value={theme['qRZXLXX'] || ''} change={e => insertTheme('qRZXLXX', e)} />
              </div>
              <div>
                {/* <AdaptiveTextDiv text={theme['qRZXWXX']} divHeight={25} /> */}
                <EditDiv unKey={'qRZXWXX'} value={theme['qRZXWXX'] || ''} change={e => insertTheme('qRZXWXX', e)} />
              </div>
            </td>
          </tr>
          <tr
            style={{ ...eduEdit, height: 60 }}
            onClick={() => {
              // setEducation(true);
            }}
          >
            <td>
              在&emsp;职
              <br />
              教&emsp;育
            </td>
            <td colSpan={2} className={styles.edu} style={{ width: 160 }}>
              <div style={{ borderBottom: '1px dashed #ccc', overflow: 'hidden' }}>
                {/* <AdaptiveTextDiv text={theme['zZXL']} divHeight={25} /> */}
                <EditDiv unKey={theme['a0000'] + 'zZXL'} value={theme['zZXL'] || ''} change={e => insertTheme('zZXL', e)} />
              </div>
              <div style={{ overflow: 'hidden' }}>
                {/* <AdaptiveTextDiv text={theme['zZXW']} divHeight={25} /> */}
                <EditDiv unKey={theme['a0000'] + 'zZXW'} value={theme['zZXW'] || ''} change={e => insertTheme('zZXW', e)} />
              </div>
            </td>
            <td>
              毕业院校
              <br />
              系及专业
            </td>
            <td colSpan={2} className={styles.eduPro}>
              <div style={{ borderBottom: '1px dashed #ccc', overflow: 'hidden' }}>
                {/* <AdaptiveTextDiv text={theme['zZXLXX']} divHeight={25} /> */}
                <EditDiv unKey={'zZXLXX'} value={theme['zZXLXX'] || ''} change={e => insertTheme('zZXLXX', e)} />
              </div>
              <div style={{ overflow: 'hidden' }}>
                {/* <AdaptiveTextDiv text={theme['zZXWXX']} divHeight={25} /> */}
                <EditDiv unKey={'zZXWXX'} value={theme['zZXWXX'] || ''} change={e => insertTheme('zZXWXX', e)} />
              </div>
            </td>
          </tr>
          <tr style={dutyEdit}>
            {`${pathname}`.startsWith('/career') ? (
              <td colSpan={2}>现任职务（岗位）</td>
            ) : `${pathname}`.startsWith('/stateOwned') || `${pathname}`.startsWith('/beInOffice/appointDismiss/archive/personnel') ? (
              <td colSpan={2}>现任职务</td>
            ) : (
              <td colSpan={2}>现任职务（职级）</td>
            )}
            <td
              colSpan={5}
              style={{ width: 510 }}
              className={styles.showLeft}
              onClick={() => {
                // if (!`${pathname}`.startsWith('/middle')) {
                //   setPosition(true);
                // }
                // setPosition(true);
              }}
            >
              {/* <div className={styles.showLeft}>{theme['a0192A'] || ''}</div> */}
              <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt' }} unKey={'a0192'} value={theme['a0192'] || ''} change={e => insertTheme('a0192', e)} />
            </td>
          </tr>
          {/* {`${pathname}`.startsWith('/beInOffice/appointDismiss/archive/personnel') && ( */}
          {
            <Fragment>
              <tr style={dutyEdit}>
                <td colSpan={2}>拟任职务</td>
                <td
                  colSpan={5}
                  style={{ width: 510 }}
                  className={styles.showLeft}
                  onClick={() => {
                    // setPosition(true);
                  }}
                >
                  {/* <div className={styles.showLeft}>{theme['proposePosition'] || ''}</div> */}
                  {(() => {
                    console.log(basicInfoData['nRZW'], 'basicInfoData[nRZW]');
                    return <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt' }} unKey={'basicInfoData[nRZW]'} value={basicInfoData['nRZW'] || ''} change={e => insertTheme('nRZW', e)} />;
                  })()}
                </td>
              </tr>
              <tr style={dutyEdit}>
                <td colSpan={2}>拟免职务</td>
                <td
                  colSpan={5}
                  style={{ width: 510 }}
                  onClick={() => {
                    // setPosition(true);
                  }}
                >
                  {/* <div className={styles.showLeft}>{theme['proposedRemovalPosition'] || ''}</div> */}
                  <EditDiv style={{ justifyContent: 'start', alignItems: 'left', fontSize: '12pt' }} unKey={'basicInfoData[nMZW]'} value={basicInfoData['nMZW'] || ''} change={e => insertTheme('nMZW', e)} />
                </td>
              </tr>
            </Fragment>
          }
        </tbody>
      </table>
      <table className={`${styles.appoint} ${styles.appoint2}`} style={{ pointerEvents }}>
        <tbody>
          <tr>
            <td className={styles.fWidth}>
              简<br />
              <br />
              <br />
              <br />历<br />
              <br />
              <span title={'简历格式化'} className={styles.format} onClick={() => a1701Format()}>
                <FileSyncOutlined />
              </span>
            </td>
            <td className={styles.resume} style={{ width: 630, height: '100%' }}>
              <Resume id={props['id']} />
            </td>
          </tr>
        </tbody>
      </table>
    </React.Fragment>
  );
}
