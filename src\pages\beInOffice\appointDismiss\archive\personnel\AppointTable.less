.main {
  display: flex;
  justify-content: center;
  font-size: 12pt !important;

  // margin-top: 10px;
  //align-items: center;
  .form {
    :global(.ant-input-affix-wrapper) {
      input {
        padding: 0;
        text-align: center;
      }
    }

    :global(.ant-input-suffix) {
      i {
        display: none;
      }
    }

    .inputTime {
      :global(.ant-input-suffix) {
        right: 4px;
      }
    }

    :global(.ant-calendar-picker .anticon-calendar) {
      display: none;
    }

    :global(.ant-calendar-picker-input) {
      text-align: center;
      padding: 0;
      height: 27px;
    }

    &>div {
      display: inline-block;
      vertical-align: top;
    }

    &>div:last-child {
      margin-left: 16px;
    }
  }

  :global {
    td {
      background: #fff !important;
    }

    .ant-input {
      border: none !important;
    }
  }
}

.situation {
  td {
    height: 98px;
  }
}

.reportUnit {
  position: relative;
  top: 20px;
}

.appoint {
  text-align: center;
  border-collapse: collapse;
  font-size: 12pt;
  table-layout: fixed;

  :global {
    .ant-input-affix-wrapper {

      &:hover,
      :focus {
        .ant-input-suffix {
          display: block;
        }
      }
    }

    .ant-input-suffix {
      display: none;
    }

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      border: none;
    }

    .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
      padding-right: 0;
    }

    .ant-input-suffix {
      margin: 0;
    }

    .ant-input-affix-wrapper {
      border: none;
    }

    .ant-input:focus {
      outline: none;
      box-shadow: none;
    }

    .ant-select-selection {
      border: none;
      outline: none;
      box-shadow: none;
    }

    .ant-select-arrow {
      display: none;
    }
  }

  input {
    border: none;
    outline: none;
    text-align: center;
  }

  tr {
    min-height: 50px;

    td:nth-child(2n-1) {
      background-color: rgb(233, 237, 246);
    }
  }

  td {
    height: 50px;
    border: 1px solid #000;
    padding: 0 6px;
    word-break: break-all;
    word-wrap: break-word;
  }
}
