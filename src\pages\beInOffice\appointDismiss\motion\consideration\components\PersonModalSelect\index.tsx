/**
 * 人员选择模态框组件
 *
 * 注意：本组件直接使用了 DictTreeSelect 和 DictSelect 组件，
 * 由于这些组件是类组件，与 TypeScript 的类型系统存在兼容性问题，
 * 因此使用 @ts-ignore 注释忽略类型检查。
 * 实际运行时不会有问题。
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Modal, Input, Button, Tabs, message, Pagination, Tag, Upload, Popconfirm } from 'antd';
import { SearchOutlined, CloseCircleOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from './index.less';
import request from '@/utils/request';
import Tree from '@/components/Hook/Tree';
import ListTable from '@/components/Hook/ListTable';
import QueryTabPane from './QueryTabPane';
import ConfirmInfoModal, { ConfirmInfoValues } from './ConfirmInfoModal';
import { getOrgTreeFromCache, saveOrgTreeToCache } from '@/utils/dexie-db';
import { changeListPayQuery } from '@/utils/method';
import uuid from 'uuid/v1';
const { TabPane } = Tabs;

// 岗位信息接口定义
interface PositionType {
  value?: string;
  label?: string;
  chooseType?: string;
}

// 行项目接口定义
interface RowItem {
  id: string;
  position?: PositionType;
  chooseType?: string;
  [key: string]: any;
}

interface PersonType {
  A00?: string; // 人员唯一标识
  A0101?: string; // 姓名
  XRZW?: string; // 现任职务
  A0107?: string; // 出生年月
  A0104?: string; // 性别
  A0141?: string; // 政治面貌
  id?: string;
  name?: string;
  [key: string]: any; // 允许其他属性
}

// 增强的人员类型，包含确认信息
export interface EnhancedPersonType extends PersonType {
  // 使用意见代码(单选)
  jobChangeCode?: string;
  // 使用意见(单选)
  jobChange?: string;
  // 现任职务代码
  currentPositionCode?: string;
  // 现任职务
  currentPosition?: string;
  // 拟任职务代码
  proposePositionCode?: string;
  // 拟任职务
  proposePosition?: string;
  // 拟免职务代码
  proposedRemovalPositionCode?: string;
  // 拟免职务
  proposedRemovalPosition?: string;
  // 任用理由
  reason?: string;
}

// 树节点类型
interface TreeNodeType {
  name: string;
  code: string;
  b0114?: string;
  children?: TreeNodeType[];
}

interface PersonModalSelectProps {
  value?: EnhancedPersonType | EnhancedPersonType[]; // 支持单个对象或对象数组
  onChange?: (value: EnhancedPersonType | EnhancedPersonType[]) => void; // 支持返回单个对象或对象数组
  title?: string;
  children?: React.ReactNode;
  planCode?: string; // 计划代码，用于任免表导入
  currentRowData?: RowItem | null; // 当前行数据，用于获取拟任职务信息
  multipleSelect?: boolean; // 是否支持多选
  skipConfirmInfo?: boolean; // 是否跳过确认信息
  maxCount?: number; // 最大选择数量
  resetSelected?: boolean; // 是否在每次打开时重置已选择的人员
  filterPersonList?: string[];
  isShowConfirmInfoModal?: boolean; // 是否显示确认信息弹窗
  // 任免表请求相关
  AppointmentData?: {
    prepareCadresCode?: string;
    planCode?: string;
  };
  saveA01Info: boolean; // 是否保存任免表
}

// API接口路径常量
const API_PATHS = {
  ORG_TREE: '/api/swzzbappoint/org/getOrgTree', // 获取组织机构树
  // ORG_TREE: '/api/plan/getAddOrgTree', // 获取组织机构树  2025/6/10 杨观明  换请求树地址
  PERSON_LIST: '/api/swzzbappoint/prepareCadres/getPrepareCadres', // 获取干部人员列表
  IMPORTED_PERSON_LIST: '/api/swzzbappoint/prepareCadres/findPersonnelData', // 获取已导入的人员列表
  DELETE_IMPORTED_PERSON: '/api/swzzbappoint/prepareCadres/deletePersonnelData', // 删除导入的人员
  SAVE_PERSONNEL_DATA: '/api/swzzbappoint/prepareCadres/savePersonnelData', // 保存人员数据
  GET_A01_TEMP_LIST: '/api/appoint/getA01TempList', // 获取临时导入的人员列表
  UPLOAD_FILE: '/api/appoint/addAppointmemt', // 上传任免表文件
};

const PersonModalSelect: React.FC<PersonModalSelectProps> = ({
  value,
  onChange,
  title = '人员选择',
  children,
  planCode = '', // 默认为空字符串
  currentRowData,
  multipleSelect = false,
  skipConfirmInfo = false,
  maxCount = 1,
  resetSelected = false,
  filterPersonList = [],
  isShowConfirmInfoModal = false,
  AppointmentData,
  saveA01Info = true,
}) => {
  const [visible, setVisible] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [personList, setPersonList] = useState<PersonType[]>([]);
  const [pagination, setPagination] = useState({});

  // 根据multipleSelect选择使用单选或多选状态
  const [selectedPerson, setSelectedPerson] = useState<PersonType | null>(null);
  const [selectedPersons, setSelectedPersons] = useState<PersonType[]>([]);
  const [selectedRowKey, setSelectedRowKey] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<TreeNodeType[]>([]);
  const [selectedOrgCode, setSelectedOrgCode] = useState('');
  const [pageNum, setPageNum] = useState(1);
  const [importedPersonList, setImportedPersonList] = useState<PersonType[]>([]);
  const [importLoading, setImportLoading] = useState(false);
  const [importPagination, setImportPagination] = useState({});
  const [currentUuid, setCurrentUuid] = useState('');

  // 确认弹窗状态
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 请求头
  const HEADERS = useMemo(
    () => ({
      Authorization: sessionStorage.getItem('authorization') || '',
      enctype: 'multipart/form-data',
    }),
    []
  );

  // 初始化组件
  useEffect(() => {
    // 如果外部传入了初始值，则同步到内部状态
    if (value) {
      if (multipleSelect) {
        // 多选模式
        const persons = Array.isArray(value) ? value : [value];
        setSelectedPersons(persons);
        setSelectedRowKeys(persons.map(p => p.id || p.A00 || '').filter(Boolean));
      } else {
        // 单选模式
        const person = Array.isArray(value) ? value[0] : value;
        setSelectedPerson(person);
        setSelectedRowKey(person?.id || person?.A00 || '');
      }
    }
  }, [value, multipleSelect]);

  // 加载组织树数据
  const loadTreeData = useCallback(async () => {
    console.log('loadTreeData', selectedOrgCode);
    try {
      // 首先尝试从缓存获取机构树数据
      const cachedData = await getOrgTreeFromCache();

      if (cachedData) {
        setTreeData(cachedData);
        // 默认获取第一个节点的机构代码，并加载相应人员列表
        if (cachedData.length > 0 && cachedData[0].b0114 && selectedOrgCode) {
          setSelectedOrgCode(cachedData[0].b0114);
          loadPersonList('', cachedData[0].b0114);
        }
        return;
      }

      // 缓存中没有数据，则从API获取
      const response = await request(API_PATHS.ORG_TREE, {
        method: 'GET',
      });

      if (response && response.code === 0) {
        const treeResult = response.data[0] || [];
        setTreeData(treeResult);

        // 存储到全局缓存
        await saveOrgTreeToCache(treeResult);

        // 2025/6/30  不需要再第一次进去的时候发送人员请求
        // 默认获取第一个节点的机构代码，并加载相应人员列表
        if (treeResult.length > 0 && treeResult[0].b0114 && selectedOrgCode) {
          setSelectedOrgCode(treeResult[0].b0114);
          loadPersonList('', treeResult[0].b0114);
        }
      }
    } catch (error) {
      console.error('加载组织树失败', error);
      message.error('加载组织树失败');
    }
  }, []);

  // 带页码参数的加载人员列表
  const loadPersonListWithPage = useCallback(
    async (keyword = '', orgCode = selectedOrgCode, page = 1) => {
      try {
        setLoading(true);
        const requestData = {
          data: {
            B0114: orgCode || '',
            A0101: keyword || '', // 姓名搜索
            pageNum: page,
            pageSize: 50, // 固定为50
          },
        };

        const response = await request(API_PATHS.PERSON_LIST, {
          method: 'POST',
          body: requestData,
        });

        if (response && response.code === 0) {
          const formattedData = changeListPayQuery(response.data);
          // 处理数据，确保每条记录都有A0192字段
          const processedList = formattedData.list.map(item => ({
            ...item,
            // 确保A0192字段有值，如果没有则尝试从其他字段获取或使用空字符串
            XRZW: item.XRZW || item.XRZW || item.presentPosition || item.position || '',
            // 转换ID字段，确保一致性
            id: item.A00 || item.A00 || item.id || '',
          }));
          console.log('加载人员列表成功，数据示例:', processedList[0]);
          setPersonList(processedList);
          setPagination({
            ...formattedData.pagination,
          });
        } else {
          message.error(response?.message || '加载人员列表失败');
          setPersonList([]);
          setPagination({});
        }
      } catch (error) {
        console.error('加载人员列表失败', error);
        message.error('加载人员列表失败');
        setPersonList([]);
        setPagination({});
      } finally {
        setLoading(false);
      }
    },
    [selectedOrgCode]
  );

  // 加载人员列表（使用当前页码）
  const loadPersonList = useCallback(
    (keyword = '', orgCode = selectedOrgCode) => {
      loadPersonListWithPage(keyword, orgCode, pageNum);
    },
    [loadPersonListWithPage, pageNum, selectedOrgCode]
  );

  // 加载导入人员列表
  const loadImportedPersonList = useCallback(async () => {
    try {
      setImportLoading(true);
      const response = await request(`${API_PATHS.IMPORTED_PERSON_LIST}?planCode=${planCode}`);

      if (response && response.code === 0) {
        const formattedData = changeListPayQuery(response.data);
        // 转换字段名称，保持与组件使用的字段名一致
        const processedList = (formattedData.list || []).map(item => ({
          A00: item.code,
          id: item.code,
          A0101: item.name, // 使用name字段作为A0101
          XRZW: item.presentPosition || item.position || '', // 确保A0192字段有值
          presentPosition: item.presentPosition || '',
          A0104: item.A0104,
          A0104Name: item.A0104Name,
          A0107: item.A0107,
          fileName: item.fileName || item.name, // 文件名使用fileName字段，如果没有则使用name
          importTime: item.createTime,
          ...item,
        }));

        setImportedPersonList(processedList);
        setImportPagination({
          current: formattedData.pageNumber,
          pageSize: formattedData.pageSize,
          total: formattedData.totalRow,
        });
      } else {
        message.error(response?.message || '加载导入人员列表失败');
        setImportedPersonList([]);
        setImportPagination({});
      }
    } catch (error) {
      console.error('加载导入人员列表失败', error);
      message.error('加载导入人员列表失败');
      setImportedPersonList([]);
      setImportPagination({});
    } finally {
      setImportLoading(false);
    }
  }, [planCode]);

  // 当弹窗显示时加载数据
  useEffect(() => {
    if (visible) {
      loadTreeData();
    }
  }, [visible, loadTreeData]);

  // 当切换到任免表导入标签页时加载导入的人员列表
  useEffect(() => {
    if (visible && activeTab === '3') {
      loadImportedPersonList();
    }
  }, [visible, activeTab, loadImportedPersonList]);

  // 展示弹窗
  const showModal = useCallback(() => {
    const newUuid = uuid();
    setCurrentUuid(newUuid);

    // 如果需要重置已选择的人员
    if (resetSelected) {
      if (multipleSelect) {
        setSelectedPersons([]);
        setSelectedRowKeys([]);
      } else {
        setSelectedPerson(null);
        setSelectedRowKey('');
      }
    }

    setVisible(true);
  }, [resetSelected, multipleSelect]);

  // 处理确认选择
  const handleConfirm = useCallback(() => {
    console.log('handleConfirm', selectedPersons);
    if (multipleSelect) {
      // 多选模式
      if (selectedPersons.length === 0) {
        message.warning('请至少选择一个人员');
        return;
      }

      if (skipConfirmInfo) {
        // 跳过确认信息直接返回结果
        if (onChange) {
          onChange(selectedPersons);
        }
        setVisible(false);
      } else {
        // 需要确认信息
        if (isShowConfirmInfoModal) {
          setConfirmModalVisible(true);
        } else {
          // 不显示确认弹窗，直接获取简历信息并返回
          if (onChange) {
            // 为多选的每个人员获取简历信息
            Promise.all(
              selectedPersons.map(async person => {
                const introduction = await getIntroduction(person.A00);
                return {
                  ...person,
                  introduction: introduction,
                };
              })
            ).then(enhancedPersons => {
              onChange(enhancedPersons);
              setVisible(false);
            });
          }
        }
      }
    } else {
      // 单选模式
      if (!selectedPerson) {
        message.warning('请选择一个人员');
        return;
      }
      if (skipConfirmInfo) {
        // 跳过确认信息直接返回结果
        if (onChange) {
          onChange(selectedPerson);
        }
        setVisible(false);
      } else {
        // 需要确认信息
        if (isShowConfirmInfoModal) {
          setConfirmModalVisible(true);
        } else {
          if (onChange) {
            getIntroduction(selectedPerson.A00).then(res => {
              onChange({
                ...selectedPerson,
                introduction: res,
              });
            });
            setVisible(false);
          }
        }
      }
    }
  }, [multipleSelect, selectedPerson, selectedPersons, skipConfirmInfo, isShowConfirmInfoModal, onChange]);

  // 没有简介 想后端获取简介
  const getIntroduction = async A00 => {
    try {
      const res = await request(`/api/swzzbappoint/prepareCadres/findCandidateData?cantidateCode=${A00}`, {
        method: 'GET',
      });
      if (res && res.code == 0 && res.data) {
        console.log(res.data);
        return res.data;
      }
    } catch (error) {}
  };

  // 处理查询面板选择
  const handleQueryPaneSelect = useCallback(
    (person: PersonType | PersonType[]) => {
      if (multipleSelect) {
        // 多选模式
        const persons = Array.isArray(person) ? person : [person];
        setSelectedPersons(persons);
        setSelectedRowKeys(persons.map(p => p.id || p.A00 || '').filter(Boolean));
      } else {
        // 单选模式
        const singlePerson = Array.isArray(person) ? person[0] : person;
        setSelectedPerson(singlePerson);
        setSelectedRowKey(singlePerson?.id || singlePerson?.A00 || '');
      }
      if (isShowConfirmInfoModal) {
        setConfirmModalVisible(true);
      }
    },
    [multipleSelect]
  );
  // 处理确认信息提交
  const handleConfirmInfoSubmit = useCallback(
    (values: ConfirmInfoValues) => {
      setConfirmLoading(true);

      try {
        if (multipleSelect) {
          // 多选模式
          const enhancedPersons = selectedPersons.map(person => ({
            ...person,
            ...values,
          }));

          if (onChange) {
            onChange(enhancedPersons);
          }
        } else {
          // 单选模式
          if (selectedPerson && onChange) {
            const enhancedPerson: EnhancedPersonType = {
              ...selectedPerson,
              ...values,
            };
            onChange(enhancedPerson);
          }
        }
        setConfirmModalVisible(false);
        setVisible(false);
      } catch (error) {
        console.error('处理确认信息失败', error);
        message.error('处理确认信息失败');
      } finally {
        setConfirmLoading(false);
      }
    },
    [multipleSelect, selectedPerson, selectedPersons, onChange]
  );

  // 取消选择
  const handleCancel = useCallback(() => {
    setVisible(false);
    // 关闭弹窗的时候 重置机构数code
  }, []);

  // 取消确认弹窗
  const handleConfirmCancel = useCallback(() => {
    setConfirmModalVisible(false);
  }, []);

  // 处理行选择变化（单选）
  const handleSelectChange = useCallback((keys: string[], rows: PersonType[]) => {
    if (keys.length > 0 && rows.length > 0) {
      const selectedRow = rows[0];
      // 确保包含现任职务信息
      const personWithPosition = {
        ...selectedRow,
        XRZW: selectedRow.XRZW || selectedRow.presentPosition || selectedRow.position || '',
      };
      console.log('选择人员(通过表格选择):', personWithPosition);
      setSelectedRowKey(keys[0]);
      setSelectedPerson(personWithPosition);
    } else {
      setSelectedRowKey('');
      setSelectedPerson(null);
    }
  }, []);

  // 处理行选择变化（多选）
  const handleMultipleSelectChange = useCallback((keys: string[], rows: PersonType[]) => {
    console.log('多选人员(通过表格选择):', keys, rows);

    // 确保包含现任职务信息
    const personsWithPosition = rows.map(row => ({
      ...row,
      XRZW: row.XRZW || row.presentPosition || row.position || '',
    }));

    setSelectedRowKeys(keys);
    setSelectedPersons(personsWithPosition);
  }, []);

  // 单击行选择人员
  const handleRowClick = useCallback(
    async (record: PersonType) => {
      const recordId = record.A00 || record.id || '';

      // 如果人员在过滤列表中，不允许选择
      if (filterPersonList.includes(recordId)) {
        message.warning('该人员已被选择，不能重复选择');
        return;
      }

      if (multipleSelect) {
        // 多选模式
        const personWithPosition = {
          ...record,
          XRZW: record.XRZW || record.presentPosition || record.position || '',
        };

        if (selectedRowKeys.includes(recordId)) {
          // 已选中，取消选择
          const newKeys = selectedRowKeys.filter(key => key !== recordId);
          const newPersons = selectedPersons.filter(person => (person.A00 || person.id) !== recordId);
          setSelectedRowKeys(newKeys);
          setSelectedPersons(newPersons);
        } else {
          // 未选中，添加选择
          setSelectedRowKeys([...selectedRowKeys, recordId]);
          setSelectedPersons([...selectedPersons, personWithPosition]);
        }
        console.log('多选人员(通过行点击):', recordId, personWithPosition);
      } else {
        // 单选模式
        if (selectedRowKey === recordId) {
          // 已选中，取消选择
          setSelectedRowKey('');
          setSelectedPerson(null);
        } else {
          // 未选中，选择此人
          // 确保包含现任职务信息
          const personWithPosition1 = {
            ...record,
            // 确保A0192字段有值，如果没有则尝试从其他字段获取或使用空字符串
            XRZW: record.XRZW || record.presentPosition || record.position || '',
          };
          console.log('选择人员(通过行点击):', personWithPosition1);
          setSelectedRowKey(recordId);
          await setSelectedPerson(personWithPosition1);
        }
      }
    },
    [multipleSelect, selectedRowKey, selectedRowKeys, selectedPersons, filterPersonList]
  );

  // 搜索
  const handleSearch = useCallback(() => {
    setPageNum(1);
    loadPersonList(searchKeyword);
  }, [searchKeyword, loadPersonList]);

  // 搜索框按Enter键
  const handleKeyPress = useCallback(
    e => {
      if (e.key === 'Enter') {
        handleSearch();
      }
    },
    [handleSearch]
  );

  // 处理树节点选择
  const handleTreeSelect = useCallback(
    (selectedKeys, e) => {
      if (selectedKeys.length > 0) {
        const orgCode = selectedKeys[0];
        setSelectedOrgCode(orgCode);
        setPageNum(1);
        loadPersonList('', orgCode);
      }
    },
    [loadPersonList]
  );
  // 处理分页变化
  const handlePageChange = useCallback(
    (page, pageSize) => {
      loadPersonListWithPage(searchKeyword, selectedOrgCode, page);
    },
    [loadPersonListWithPage, searchKeyword, selectedOrgCode]
  );

  const handleDeleteImportedFile = useCallback(
    async (record: PersonType, e?: React.MouseEvent) => {
      e && e.stopPropagation();

      try {
        const response = await request(`${API_PATHS.DELETE_IMPORTED_PERSON}?code=${record.id || record.A00 || ''}`, {
          method: 'GET',
        });

        if (response && response.code === 0) {
          message.success('删除成功');

          // 如果被删除的人是当前选中的人员，需要清空选择
          const recordId = record.A00 || record.id || '';
          if (recordId && selectedRowKey === recordId) {
            setSelectedRowKey('');
            setSelectedPerson(null);
          }

          // 重新加载列表
          loadImportedPersonList();
        } else {
          message.error(response?.message || '删除失败');
        }
      } catch (error) {
        console.error('删除导入文件失败', error);
        message.error('删除失败');
      }
    },
    [selectedRowKey, loadImportedPersonList]
  );

  // 处理文件上传
  const handleFileUpload = useCallback(
    async info => {
      if (info.file.status === 'done') {
        try {
          if (`${info.file.response.code}` !== '0') {
            message.error(info.file.response?.message || '文件上传失败');
            return;
          }
          //2025/6/9 申余清 这个接口不用了 直接调用SAVE_PERSONNEL_DATA  将currentUuid作为codes传过去
          // const res = await request(`${API_PATHS.GET_A01_TEMP_LIST}?pageSize=10&pageNum=1&impId=${currentUuid}`);

          // let codes: string[] = [];
          // if (res.code === '0') {
          //   const { list = [] } = changeListPayQuery(res.data);
          //   codes = list.map((item: any) => item.A00);
          // }

          // if (codes.length > 0) {
          const response = await request(API_PATHS.SAVE_PERSONNEL_DATA, {
            method: 'POST',
            body: {
              data: {
                planCode,
                codes: currentUuid,
              },
            },
          });

          if (response && response.code === 0) {
            loadImportedPersonList();
          } else {
            message.error(response?.message || '文件导入失败');
          }
          // }
        } catch (error) {
          console.error('导入人员数据失败', error);
          message.error('文件导入失败');
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
    [currentUuid, planCode, loadImportedPersonList]
  );

  // 表格列定义
  const columns = useMemo(
    () => [
      {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        render: (text, record, index) => index + 1,
      },
      {
        title: '姓名',
        dataIndex: 'A0101',
        width: 80,
        align: 'center',
      },
      {
        title: '现任职务',
        dataIndex: 'XRZW',
      },
      {
        title: '性别',
        dataIndex: 'A0104',
        width: 60,
        align: 'center',
      },
      {
        title: '出生年月',
        dataIndex: 'A0107',
        width: 120,
        align: 'center',
      },
      {
        title: '政治面貌',
        dataIndex: 'A0141',
        width: 100,
        align: 'center',
      },
      {
        title: '操作',
        key: 'action',
        width: 80,
        align: 'center',
        render: (text, record) => {
          const recordId = record.A00 || record.id || '';
          // 如果人员在过滤列表中，显示禁用按钮
          if (filterPersonList.includes(recordId)) {
            return (
              <Button type="link" disabled style={{ color: '#d9d9d9' }}>
                已选择
              </Button>
            );
          }
          return (
            <Button type="link" onClick={() => handleRowClick(record)}>
              {selectedRowKey === recordId ? '取消' : '选择'}
            </Button>
          );
        },
      },
    ],
    [handleRowClick, selectedRowKey]
  );

  // 导入列表的列定义
  const importedColumns = useMemo(
    () => [
      {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        render: (text, record, index) => index + 1,
      },
      {
        title: '姓名',
        dataIndex: 'A0101',
        width: 80,
      },
      {
        title: '现任职务',
        dataIndex: 'XRZW',
        width: 180,
      },
      {
        title: '性别',
        dataIndex: 'A0104Name',
        width: 60,
      },
      {
        title: '出生年月',
        dataIndex: 'A0107',
        width: 100,
      },
      {
        title: '导入时间',
        dataIndex: 'importTime',
        width: 140,
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (text, record) => {
          const recordId = record.A00 || record.id || '';
          // 如果人员在过滤列表中，显示禁用按钮
          const isDisabled = filterPersonList.includes(recordId);

          return (
            <>
              <Button type="link" onClick={() => handleRowClick(record)} style={{ marginRight: '8px' }} disabled={isDisabled}>
                {isDisabled ? '已选择' : selectedRowKey === recordId ? '取消' : '选择'}
              </Button>
              <Popconfirm
                title={`确定要删除 ${record.A0101 || '选中人员'} 吗？`}
                onConfirm={e => handleDeleteImportedFile(record, e)}
                okText="确认"
                cancelText="取消"
                onCancel={e => e?.stopPropagation()}
              >
                <Button type="link" danger onClick={e => e.stopPropagation()}>
                  删除
                </Button>
              </Popconfirm>
            </>
          );
        },
      },
    ],
    [handleRowClick, handleDeleteImportedFile, selectedRowKey]
  );

  // 表格行选择配置（根据multipleSelect动态切换单选/多选）
  const rowSelection = useMemo(
    () => ({
      type: multipleSelect ? ('checkbox' as const) : ('radio' as const),
      selectedRowKeys: multipleSelect ? selectedRowKeys : selectedRowKey ? [selectedRowKey] : [],
      onChange: multipleSelect ? handleMultipleSelectChange : handleSelectChange,
      getCheckboxProps: record => ({
        // 禁用条件：如果人员已在过滤列表中，则禁用选择
        disabled: filterPersonList.includes(record.A00 || ''),
      }),
    }),
    [multipleSelect, selectedRowKey, selectedRowKeys, handleSelectChange, handleMultipleSelectChange, filterPersonList]
  );

  // 渲染选中的人员信息
  const renderSelectedPerson = useCallback(() => {
    return (
      <React.Fragment>
        {multipleSelect
          ? // 多选模式显示
            selectedPersons.length > 0 && (
              <div style={{ display: 'flex' }}>
                <div style={{ width: '90px' }}>已选人员：</div>
                <div>{selectedPersons.map(person => `${person.A0101}${person.XRZW ? `，${person.XRZW}` : ''}`).join('、')}</div>
              </div>
            )
          : // 单选模式显示
            selectedPerson && (
              <div style={{ display: 'flex' }}>
                <div>已选人员：</div>
                <div>{selectedPerson.A0101}</div>
                <div>{selectedPerson.XRZW ? `，${selectedPerson.XRZW}` : ''}</div>
              </div>
            )}
      </React.Fragment>
    );
  }, [multipleSelect, selectedPerson, selectedPersons]);

  // 渲染触发器按钮
  const renderTrigger = useCallback(() => {
    return React.cloneElement(children as React.ReactElement, {
      onClick: showModal,
    });
  }, [children, showModal, value]);

  //过滤函数 - 不应该从列表中排除，而是在UI中禁用选择
  const filterPersonHandler = data => {
    // 返回原始数据，不做过滤
    return data;
  };

  return (
    <>
      {children ? (
        renderTrigger()
      ) : (
        <Button type="link" onClick={showModal} style={{ padding: 0 }}>
          {value ? (Array.isArray(value) ? value[0]?.A0101 || value[0]?.name || '请选择人员' : value.A0101 || value.name || '请选择人员') : '请选择人员'}
        </Button>
      )}

      <Modal
        title={title}
        visible={visible}
        maskClosable={false}
        onOk={handleConfirm}
        onCancel={handleCancel}
        width={1300}
        bodyStyle={{ height: 600, padding: '0 16px' }}
        className={styles.personModalSelect}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="人员选择" key="1">
            <div className={styles.personSelectContainer}>
              <div className={styles.treeContainer}>
                <Tree treeData={treeData} onSelect={handleTreeSelect} showLine nodeTitle="name" nodeCode="b0114" style={{ height: 520 }} />
              </div>

              <div className={styles.content}>
                <div className={styles.searchBox}>
                  {renderSelectedPerson()}
                  <Input
                    placeholder="姓名"
                    value={searchKeyword}
                    onChange={e => setSearchKeyword(e.target.value)}
                    onKeyPress={handleKeyPress}
                    suffix={<SearchOutlined onClick={handleSearch} />}
                    style={{ width: 300 }}
                  />
                </div>
                <div>
                  <ListTable
                    columns={columns}
                    data={filterPersonHandler(personList)}
                    rowKey="A00"
                    loading={loading}
                    scroll={{ y: 380 }}
                    rowSelection={rowSelection}
                    onRow={record => ({
                      onClick: () => handleRowClick(record),
                      // 为已在过滤列表中的行添加灰色背景和禁用样式
                      style: filterPersonList.includes(record.A00) ? { backgroundColor: '#f5f5f5', color: '#d9d9d9', cursor: 'not-allowed' } : {},
                    })}
                    pagination={pagination}
                    onChange={handlePageChange}
                  />
                </div>
              </div>
            </div>
          </TabPane>

          <TabPane tab="查询选择" key="2">
            <QueryTabPane
              styles={styles}
              selectedPerson={multipleSelect ? selectedPersons : selectedPerson ? [selectedPerson] : []}
              handleSelectPerson={handleQueryPaneSelect}
              maxCount={maxCount}
              singleSelect={!multipleSelect}
              filterPersonList={filterPersonList}
            />
          </TabPane>

          <TabPane tab="任免表导入" key="3">
            <div className={styles.importContainer}>
              <div className={styles.uploadSection}>
                <Upload name="file" action={`${API_PATHS.UPLOAD_FILE}?impId=${currentUuid}`} headers={HEADERS} multiple={true} showUploadList={false} onChange={handleFileUpload}>
                  <Button type="primary">上传任免表</Button>
                </Upload>
              </div>

              <div className={styles.tableSection}>
                <ListTable
                  columns={importedColumns}
                  data={filterPersonHandler(importedPersonList) || []}
                  rowKey="A00"
                  loading={importLoading}
                  rowSelection={rowSelection}
                  onRow={record => ({
                    onClick: () => handleRowClick(record),
                    // 为已在过滤列表中的行添加灰色背景和禁用样式
                    style: filterPersonList.includes(record.A00) ? { backgroundColor: '#f5f5f5', color: '#d9d9d9', cursor: 'not-allowed' } : {},
                  })}
                  pagination={importPagination}
                  onChange={() => loadImportedPersonList()}
                />
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Modal>

      {/* 确认信息弹窗 */}
      {!skipConfirmInfo && (
        <ConfirmInfoModal
          visible={confirmModalVisible}
          onCancel={handleConfirmCancel}
          onConfirm={handleConfirmInfoSubmit}
          isShowTab={true}
          saveA01Info={saveA01Info}
          personInfo={
            multipleSelect
              ? selectedPersons[0]
                ? { A0101: selectedPersons[0].A0101, XRZW: selectedPersons[0].XRZW, candidateCode: selectedPersons[0].A00 }
                : undefined
              : selectedPerson
              ? { A0101: selectedPerson.A0101, XRZW: selectedPerson.XRZW, candidateCode: selectedPerson.A00 }
              : undefined
          }
          title="人员信息确认"
          proposePosition={currentRowData?.position}
          AppointmentData={AppointmentData}
        />
      )}
    </>
  );
};

export default PersonModalSelect;
