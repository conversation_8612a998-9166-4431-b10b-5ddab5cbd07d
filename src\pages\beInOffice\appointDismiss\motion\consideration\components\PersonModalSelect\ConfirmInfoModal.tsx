import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio, Checkbox, Row, Col, Button, Select, Tabs } from 'antd';
import styles from './index.less';
import request from '@/utils/request';
import DictSelect from '@/components/DictSelect';
import { getDictList } from '@/services';
import AppointTable from '../../../../archive/personnel/AppointTable';
import moment from 'moment';

// 定义确认信息弹窗的属性接口
interface ConfirmInfoModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (values: ConfirmInfoValues) => void;
  personInfo?: {
    jobChangeCode?: string; // 岗位变动代码
    A0101?: string; // 姓名
    A0192?: string; // 现任职务
    XRZW?: string; // 现任职务
    introduction?: string; // 岗位简介
    reason?: string;
    candidateCode?: string;
  };
  title?: string;
  // 添加拟任职务信息
  proposePosition?: {
    value?: string;
    label?: string;
  };
  // 不显示项
  notShowItemList?: string[];
  // 添加tab页 需要控制是否显示
  isShowTab?: boolean;
  // 任免表请求相关
  AppointmentData?: {
    prepareCadresCode?: string;
    planCode?: string;
  };
  saveA01Info: boolean; // 是否保存任免表
}

// 定义表单值的接口
export interface ConfirmInfoValues {
  // 使用意见代码(单选)
  jobChangeCode?: string;
  // 使用意见(单选)
  jobChange?: string;
  // 现任职务代码
  currentPositionCode?: string;
  // 现任职务
  currentPosition?: string;
  // 拟任职务代码
  proposePositionCode?: string;
  // 拟任职务
  proposePosition?: string;
  // 拟免职务代码
  proposedRemovalPositionCode?: string;
  // 拟免职务
  proposedRemovalPosition?: string;
  // 任用理由
  reason?: string;
}

// 字典项接口
interface DictItem {
  CODE_VALUE: string;
  CODE_NAME: string;
}

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const { TabPane } = Tabs;

const ConfirmInfoModal: React.FC<ConfirmInfoModalProps> = ({
  notShowItemList,
  visible,
  onCancel,
  onConfirm,
  personInfo = {},
  title = '使用意见',
  proposePosition = {},
  isShowTab = false,
  AppointmentData,
  saveA01Info = true,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [jobChangeOptions, setJobChangeOptions] = useState<DictItem[]>([]);
  const [activeTab, setActiveTab] = useState('1');
  const [appointtableData, setAppointtableData] = useState({});
  const [xrzw, setXrzw] = useState();
  // 获取字典数据
  const fetchDictData = async () => {
    try {
      const res = await getDictList({ data: { codeType: 'SWZZBAPPIONT_JOBCHANGE' } });

      if (res && res.code === 0 && Array.isArray(res.data)) {
        setJobChangeOptions(res.data);
      }
    } catch (error) {
      console.error('获取字典数据失败', error);
    }
  };

  // 当弹窗显示时加载字典数据
  useEffect(() => {
    if (visible) {
      form.resetFields();
      fetchDictData();
      if (!personInfo.introduction) {
        getIntroduction();
      }
      if (isShowTab) {
        getAppointmentData();
      }
      // 设置初始值
      const initialValues = {
        // 如果有现任职务信息，自动填充
        currentPosition: personInfo.XRZW || '',
        // 将现任职务作为拟免职务
        proposedRemovalPosition: personInfo.XRZW || '',
        // 如果有拟任职务信息，自动填充
        proposePosition: proposePosition?.label || '',
        proposePositionCode: proposePosition?.value || '',
        // 初始化使用意见为空
        jobChangeCode: personInfo.jobChangeCode || '',
        reason: personInfo.reason || '',
        introduction: personInfo.introduction || '',
      };
      // 输出调试信息
      console.log('ConfirmInfoModal 初始化:', {
        personInfo,
        proposePosition,
        initialValues,
        visible,
      });

      form.setFieldsValue(initialValues);
    }
    // }, [visible, personInfo, proposePosition, form]);   //这里form添加了依赖，导致循环调用fetchDictData
  }, [visible]);
  // 如果introduction 简介 没有值的时候需要请求后端
  const getIntroduction = async () => {
    try {
      //简介
      request(`/api/swzzbappoint/prepareCadres/findCandidateData?cantidateCode=${personInfo?.candidateCode}`).then(res => {
        if (res && res.code == 0 && res.data) {
          console.log(res.data);
          form.setFieldsValue({
            introduction: res.data,
          });
        }
      });

      //现任单位及职务、拟免职务
      request(`/api/swzzbappoint/prepareCadres/findCandidateXRZW?cantidateCode=${personInfo?.candidateCode}`).then(res => {
        if (res && res.code == 0 && res.data) {
          console.log(res.data);
          setXrzw(res.data);
          form.setFieldsValue({
            proposedRemovalPosition: res.data,
          });
        }
      });
    } catch (error) {}
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      // 转换使用意见数据
      if (values.jobChangeCode) {
        // 根据选中的代码找到对应的名称
        const option = jobChangeOptions.find(opt => opt.CODE_VALUE === values.jobChangeCode);
        if (option) {
          values.jobChange = option.CODE_NAME;
        }
      }

      // 确保拟免职务有值
      if (!values.proposedRemovalPosition && personInfo.XRZW) {
        values.proposedRemovalPosition = personInfo.XRZW;
      }

      onConfirm({
        ...values,
        currentPosition: personInfo.XRZW || values.proposedRemovalPosition || '',
      });
      if (isShowTab) {
        setAppointmentData(appointtableData);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
      setActiveTab('1');
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = key => {
    setActiveTab(key);
  };

  // 获取任免表数据
  const getAppointmentData = async () => {
    try {
      const res = await request(
        `/api/swzzbappoint/suggestion/findA01Info?planCode=${AppointmentData?.planCode}&prepareCadresCode=${AppointmentData?.prepareCadresCode}&a0000=${personInfo?.candidateCode}`,
        {
          method: 'GET',
        }
      );
      if (res && res.code === 0) {
        // 如果没有拟任职务、拟免职务，就把左侧列表的值赋值进去
        if (res.data) {
          if (!res.data.nRZW) {
            res.data.nRZW = proposePosition.label || '';
          }
          if (!res.data.nMZW) {
            res.data.nMZW = personInfo.XRZW || '';
          }
          if (!res.data.a0192) {
            res.data.a0192 = res.data.a0192A || '';
          }
          setAppointtableData(res.data);
        } else {
          getOldAppointmentData();
        }
      }
    } catch (error) {}
  };

  // 获取老版任免表数据
  const getOldAppointmentData = async () => {
    try {
      const res = await request(`/api/mem/getMemInfo?A0000=${personInfo?.candidateCode}`, {
        method: 'GET',
        // 设置header
        headers: {
          A0000: personInfo?.candidateCode,
          A0165: '02',
        },
      });
      if (res && res.code === 0) {
        // 如果没有拟任职务、拟免职务，就把左侧列表的值赋值进去
        if (!res.data.nRZW) {
          res.data.nRZW = proposePosition.label || '';
        }
        if (!res.data.nMZW) {
          res.data.nMZW = personInfo.XRZW || '';
        }
        if (!res.data.a0192) {
          res.data.a0192 = res.data.a0192A || '';
        }
        setAppointtableData(res.data);
        if (res.data) {
          if (isShowTab) {
            setAppointmentData(res.data);
          }
        }
      }
    } catch (error) {
      console.error('getMemInfo出错:', error);
    }
  };
  //保存任免表数据
  const setAppointmentData = async data => {
    if (!saveA01Info) {
      return;
    }
    try {
      // 这里需要处理一下数据 修改a36VoList
      const value = {
        ...data,
        planCode: AppointmentData?.planCode,
        prepareCadresCode: AppointmentData?.prepareCadresCode,
      };
      if (data.a36VoList) {
        value.a36VoList = data.a36VoList.map(item => {
          if (item.a3607) {
            item.a3607 = moment(item.a3607.replace('.', '-')).format('YYYY-MM-DD HH:mm:ss');
          }
          return item;
        });
      }
      request(`/api/swzzbappoint/suggestion/saveA01Info`, {
        method: 'POST',
        body: {
          data: value ? value : appointtableData,
        },
      });
    } catch (error) {}
  };

  const handleOnCancel = () => {
    setAppointtableData([]);
    onCancel && onCancel();
  };

  return (
    <Modal
      title={personInfo.A0101 ? `${personInfo.A0101}人事安排` : title}
      visible={visible}
      onCancel={handleOnCancel}
      footer={[
        <Button key="cancel" onClick={handleOnCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={activeTab == '2' ? 1600 : 700}
      maskClosable={false}
      destroyOnClose
    >
      {/* {personInfo?.A0101 && (
        <div className={styles.personInfoBox}>
          <div className={styles.personName}>姓名：{personInfo.A0101}</div>
          {personInfo.XRZW && <div className={styles.personPosition}>现任职务：{personInfo.XRZW}</div>}
        </div>
      )} */}
      <Tabs activeKey={activeTab} destroyInactiveTabPane={false} onChange={handleTabChange} className={styles.tabs}>
        <TabPane tab="信息确认" key="1">
          <Form
            {...formItemLayout}
            className={styles.confirmInfoForm}
            form={form}
            initialValues={{
              jobChangeCode: personInfo.jobChangeCode,
              introduction: personInfo?.introduction || '',
              currentPosition: personInfo?.XRZW || '',
              proposedRemovalPosition: personInfo?.XRZW || '',
              proposePosition: proposePosition?.label || '',
              proposePositionCode: proposePosition?.value || '',
            }}
          >
            {!notShowItemList?.includes?.('jobChangeCode') && (
              <Form.Item name="jobChangeCode" label="使用意见" rules={[{ required: true, message: '请选择使用意见' }]}>
                <Radio.Group
                  onChange={e => {
                    let val = e?.target?.value;
                    if (val === '4' || val === '5') {
                      form.setFieldsValue({ proposedRemovalPosition: '' });
                    }
                  }}
                >
                  <Row>
                    {jobChangeOptions.map(option => (
                      <Col span={8} key={option.CODE_VALUE}>
                        <Radio value={option.CODE_VALUE}>{option.CODE_NAME}</Radio>
                      </Col>
                    ))}
                  </Row>
                </Radio.Group>
              </Form.Item>
            )}
            <Form.Item label="现任单位及职务">{personInfo.XRZW || xrzw}</Form.Item>

            <Form.Item name="introduction" label="简介" rules={[{ required: true, message: '请输入简介' }]}>
              <Input.TextArea placeholder="请输入简介" rows={3} />
            </Form.Item>
            {/* <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="introduction" label="简介" rules={[{ required: true, message: '请输入简介' }]}>
              <Input placeholder="请输入简介" />
            </Form.Item>
          </Col>
        </Row> */}
            {!notShowItemList?.includes?.('proposePosition') && (
              <Form.Item name="proposePosition" label="拟任职务" rules={[{ required: true, message: '请输入拟任职务' }]}>
                <Input placeholder="请输入拟任职务" />
              </Form.Item>
            )}
            <Form.Item name="proposePositionCode" hidden>
              <Input />
            </Form.Item>

            <Form.Item name="proposedRemovalPosition" label="拟免职务">
              <Input placeholder="拟免职务" readOnly />
            </Form.Item>
            <Form.Item name="proposedRemovalPositionCode" hidden>
              <Input />
            </Form.Item>

            <Form.Item name="reason" label="使用理由" rules={[{ required: false, message: '请输入使用理由' }]}>
              <Input.TextArea rows={3} placeholder="请输入使用理由" />
            </Form.Item>
          </Form>
        </TabPane>
        {isShowTab && (
          <TabPane tab="任免表" key="2">
            <AppointTable isClick={true} person={appointtableData} setTheme={setAppointtableData} />
          </TabPane>
        )}
      </Tabs>
    </Modal>
  );
};

export default ConfirmInfoModal;
