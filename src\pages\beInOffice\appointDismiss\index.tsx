import React, { Fragment, useEffect, useState } from 'react';
import { Button, Input, Modal, Form, message, Space, Divider, Tabs, Checkbox, Popconfirm } from 'antd';
import TimePicker from '@/components/TimePicker';
import ListTable from '@/components/ListTable';
import ListPagination from '@/components/ListPagination';
import styles from './index.less';
import moment from 'moment';
import { getPlanList, addPlan, deletePlan, updatePlan, mergePlans, remergePlan, cancelMerge, sharePlan, verifyPassword } from './services';
import { changeListPayQuery } from '@/utils/method';
const { TabPane } = Tabs;
import { router } from 'umi';
import { connect } from 'dva';
// 定义方案项数据接口
interface PlanItem {
  id?: string | number;
  code: string | number;
  name: string;
  planName?: string;
  planPassword?: string;
  planContent?: string;
  createTime?: string;
  sharedDepartment?: string;
  sharedTime?: string;
  mergeStatus?: string;
  [key: string]: any;
}

// 分页接口定义
interface PaginationState {
  pageSize: number;
  total: number;
  page: number;
}

// 搜索参数接口定义
interface SearchParams {
  planName: string;
  planStartTime: string;
  planEndTime: string;
}

const AppointDismissPlan: React.FC = props => {
  const { pageType = '' } = props; // type='dqgxfa' 时表示来自动议-像书记汇报-读取共享方案 按钮

  // =========== 状态定义 ===========
  const [planList, setPlanList] = useState<PlanItem[]>([]);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    planName: '',
    planStartTime: '',
    planEndTime: '',
  });
  const [pagination, setPagination] = useState<PaginationState>({
    pageSize: 50,
    total: 0,
    page: 1,
  });
  const [addVisible, setAddVisible] = useState(false);
  const [mergeVisible, setMergeVisible] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false); // 添加密码弹窗状态
  const [currentPlan, setCurrentPlan] = useState<PlanItem | null>(null); // 当前操作的方案
  const [passwordForm] = Form.useForm(); // 添加密码表单
  const [activeTab, setActiveTab] = useState('1'); // 1-我的方案，2-提交方案
  const [form] = Form.useForm();
  const [mergeForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [selectedPlans, setSelectedPlans] = useState<PlanItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isEdit, setIsEdit] = useState(false); // 标记当前是否为编辑模式
  const [passwordRequired, setPasswordRequired] = useState(false); // 旧密码和新密码 必填权限
  const [currentEditCode, setCurrentEditCode] = useState<string | number | null>(null); // 当前编辑的方案code
  const [tableHeight, setTableHeight] = useState(600);

  // list高度
  useEffect(() => {
    const calcTableHeight = () => {
      // 获取页面总高度
      const totalHeight = window.innerHeight;
      setTableHeight(totalHeight - 400);
    };
    calcTableHeight();
    window.addEventListener('resize', calcTableHeight);
    return () => window.removeEventListener('resize', calcTableHeight);
  }, []);
  // 提交方案权限
  const purview = sessionStorage.getItem('swzzbAppointRole');
  // =========== 数据获取 ===========
  // 获取方案列表
  const fetchPlanList = async (page = pagination.page, pageSize = pagination.pageSize, params = searchParams, tableType = activeTab) => {
    setLoading(true);
    try {
      const res = await getPlanList({
        data: {
          ...params,
          pageNum: page,
          pageSize,
          tableType: tableType,
        },
      });
      if (res.code === 0) {
        const data = changeListPayQuery(res.data);
        setPlanList(data.list);
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error('获取方案列表失败', error);
    } finally {
      setLoading(false);
    }
  };

  // 在useEffect中获取路由参数
  useEffect(() => {
    // 从URL获取参数
    const query = new URLSearchParams(window.location.search);
    let tab = query.get('tab');

    // 如果URL中有tab参数，则切换到对应标签页
    if (tab && (tab === '1' || tab === '2')) {
      setActiveTab(tab);
    }
    // 来自读取共享方案按钮，只显示共享方案的内容
    if (pageType === 'dqgxfa') {
      setActiveTab('2');
      tab = '2';
    }
    fetchPlanList(1, pagination.pageSize, searchParams, tab || activeTab);
  }, []);

  // =========== 事件处理 ===========
  // 搜索方案
  const handleSearch = () => {
    fetchPlanList(1, pagination.pageSize);
  };

  // 重置搜索
  const handleReset = () => {
    const resetParams = {
      planName: '',
      planStartTime: '',
      planEndTime: '',
    };
    setSearchParams(resetParams);
    fetchPlanList(1, pagination.pageSize, resetParams);
  };

  // 分页变化
  const handlePageChange = (page, pageSize) => {
    fetchPlanList(page, pageSize);
  };

  // 打开方案
  const handleOpenPlan = async record => {
    // 设置当前方案并显示密码弹窗
    setCurrentPlan(record);
    passwordForm.resetFields();
    setPasswordVisible(true);
  };

  // 验证方案密码并打开
  const handleVerifyPassword = async () => {
    let lastData: any = props.login['menuData'] || [];
    try {
      const values = await passwordForm.validateFields();
      setSubmitting(true);

      // 获取URL参数，检查是否有planCode
      const query = new URLSearchParams(window.location.search);
      const planCodeFromUrl = query.get('planCode');

      // 验证密码
      const res = await verifyPassword({
        code: currentPlan?.code,
        password: values.password,
      });

      if (res.code === 0) {
        //查询动议下的第一个菜单并跳转
        let find = lastData.find(item => {
          //干部二处工作方案需排除动议菜单
          if (activeTab == '3') {
            return `${item.url}`.startsWith('/beInOffice/appointDismiss/motion/reportSuperior');
          }
          return `${item.url}`.startsWith('/beInOffice/appointDismiss/motion');
        });
        let url = find ? find.url : '';
        // 密码验证成功，打开方案
        // 如果有planCode参数且当前在提交方案标签页，则在跳转时加上合并参数
        router.push(`${url}?code=${currentPlan?.code}&planName=${currentPlan?.planName}&type=${activeTab}`);
      } else {
        message.error(res.message || '方案密码验证失败');
      }
    } catch (error) {
      console.error('验证密码失败', error);
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭密码弹窗
  const handlePasswordCancel = () => {
    setPasswordVisible(false);
    passwordForm.resetFields();
    setCurrentPlan(null);
  };

  // 编辑方案
  const handleEdit = async record => {
    await setIsEdit(true);

    setCurrentEditCode(record.code); // 保存当前编辑的方案code到状态中
    form.setFieldsValue({
      planName: record.planName || record.name,
      planPassword: record.planPassword,
      planContent: record.planContent,
    });
    setAddVisible(true);
  };

  // 删除方案
  const handleDeletePlan = async record => {
    try {
      const res = await deletePlan({ code: record.code });
      if (res.code === 0) {
        message.success('删除成功');
        fetchPlanList();
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理标签页切换
  const handleTabChange = key => {
    // 先清空选择的行
    setSelectedRowKeys([]);
    setSelectedPlans([]);
    router.push(`?tab=${key}`);
    // 设置当前tab并立即获取数据，避免等待useEffect触发
    setActiveTab(key);
    fetchPlanList(1, pagination.pageSize, searchParams, key);
  };

  // =========== 表单处理 ===========
  // 新增方案弹窗确认
  const handleAddConfirm = async () => {
    //这里不能放里面就会执行finally也会把isEdit设置为true 调用新增接口
    const values = await form.validateFields();
    try {
      const params = {
        ...values,
      };

      setSubmitting(true);
      let res;
      if (isEdit) {
        // 使用状态中保存的code
        res = await updatePlan({
          data: {
            ...params,
            code: currentEditCode, // 使用状态中保存的code
          },
        });
      } else {
        res = await addPlan({
          data: params,
        });
      }

      if (res.code === 0) {
        message.success(isEdit ? '修改成功' : '新增成功');
        setAddVisible(false);
        form.resetFields();
        setCurrentEditCode(null); // 重置编辑code
        fetchPlanList();
        setIsEdit(false);
      } else {
        message.error(res.message || (isEdit ? '修改失败' : '新增失败'));
      }
    } catch (error) {
      console.error(isEdit ? '修改方案失败' : '新增方案失败', error);
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭新增弹窗
  const handleAddCancel = () => {
    setAddVisible(false);
    form.resetFields();
    setIsEdit(false);
    setPasswordRequired(false);
    setCurrentEditCode(null); // 重置编辑code
  };

  // 打开新增方案弹窗
  const showAddModal = () => {
    form.resetFields();
    setIsEdit(false);
    setCurrentEditCode(null); // 重置编辑code
    setAddVisible(true);
  };

  // 打开合并方案弹窗
  const showMergeModal = async () => {
    // 获取URL中的planCode参数
    const url = new URL(window.location.href);
    let planCodeFromUrl = url.searchParams.get('planCode');
    if (pageType === 'dqgxfa') {
      planCodeFromUrl = props?.planCode;
    }
    const selected = planList.filter(item => selectedRowKeys.includes(item.code));
    setSelectedPlans(selected);

    // 如果URL中有planCode参数，直接合并而不显示弹窗
    if (planCodeFromUrl) {
      // 直接调用合并API
      const params = {
        data: {
          planCode: planCodeFromUrl,
          MergePlanCodes: selectedRowKeys,
        },
      };

      try {
        setSubmitting(true);
        const res = await mergePlans(params);

        if (res.code === 0) {
          message.success('合并成功');
          fetchPlanList();
          setSelectedRowKeys([]);
          setSelectedPlans([]);
          // 如果来自 读取共享方案：关闭弹窗
          if (pageType === 'dqgxfa') {
            props?.doBack && props.doBack();
          }
        } else {
          message.error(res.message || '合并失败');
        }
      } catch (error) {
        console.error('合并方案失败', error);
        message.error('合并失败');
      } finally {
        setSubmitting(false);
      }
    } else {
      // 没有planCode参数时，显示合并弹窗
      mergeForm.resetFields();
      setMergeVisible(true);
    }
  };

  // 合并方案弹窗确认
  const handleMergeConfirm = async () => {
    try {
      const values = await mergeForm.validateFields();
      setSubmitting(true);

      // 创建新方案 - 因为进入弹窗确认时，肯定是没有planCode的情况
      const addParams = {
        data: {
          planName: values.planName,
          planPassword: values.planPassword,
          planContent: values.planContent,
        },
      };

      const addRes = await addPlan(addParams);
      if (addRes.code !== 0) {
        message.error(addRes.message || '创建方案失败');
        return;
      }

      // 获取新建方案的code
      const planCode = addRes.data;

      // 执行合并操作
      const mergeParams = {
        data: {
          planCode,
          MergePlanCodes: selectedRowKeys,
        },
      };

      const res = await mergePlans(mergeParams);
      if (res.code === 0) {
        message.success('合并成功');
        setMergeVisible(false);
        mergeForm.resetFields();
        fetchPlanList();
        setSelectedRowKeys([]);
        setSelectedPlans([]);
        router.push(`/beInOffice/appointDismiss/motion/consideration?code=${addRes.data}&planName=${values.planName}`)
      } else {
        message.error(res.message || '合并失败');
      }
    } catch (error) {
      console.error('合并方案失败', error);
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭合并方案弹窗
  const handleMergeCancel = () => {
    setMergeVisible(false);
    mergeForm.resetFields();
  };

  // 通用的API调用处理函数
  const callApiWithFeedback = async (apiFunc, params, successMsg, errorMsg) => {
    try {
      const res = await apiFunc(params);
      if (res.code === 0) {
        message.success(successMsg);
        fetchPlanList();
        if (pageType === 'dqgxfa') {
          props?.doBack && props.doBack();
        }
        return true;
      } else {
        message.error(res.message || errorMsg);
        return false;
      }
    } catch (error) {
      console.error(errorMsg, error);
      message.error(errorMsg);
      return false;
    }
  };

  // 重新合并方案
  const handleRemergePlan = async record => {
    callApiWithFeedback(remergePlan, { data: { planCode: record.code } }, '重新合并成功', '重新合并失败');
  };

  // 取消合并方案
  const handleCancelMerge = async record => {
    callApiWithFeedback(cancelMerge, { data: { planCode: record.code } }, '取消合并成功', '取消合并失败');
  };

  // 共享/取消提交方案
  const handleSharePlan = async (record, isShare) => {
    callApiWithFeedback(sharePlan, { code: record.code, shareStatus: isShare }, isShare ? '共享成功' : '取消共享成功', isShare ? '共享失败' : '取消共享失败');
  };

  // =========== 表格配置 ===========
  // 表格列定义 - 我的方案
  const myPlanColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
      width: 80,
    },
    {
      title: '方案名称',
      dataIndex: 'planName',
      key: 'planName',
      render: (text, record) => <a onClick={() => handleOpenPlan(record)}>{text}</a>,
    },
    {
      title: '方案说明',
      dataIndex: 'planContent',
      key: 'planContent',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (_, record) => (
        <Space split={<Divider type="vertical" />}>
          <a onClick={() => handleOpenPlan(record)}>打开方案</a>
          <a onClick={() => handleEdit(record)}>修改</a>
          {/* 2025/6/9  杨观明 屏蔽 */}
          {/* <Popconfirm
            title={record.shareStatus == 1 ? '确定要取消共享该方案吗？' : '确定要共享该方案吗？'}
            okText="确定"
            cancelText="取消"
            onConfirm={() => handleSharePlan(record, record.shareStatus == 1 ? 0 : 1)}
          >
            <a>{record.shareStatus == 1 ? '取消共享' : '共享'}</a>
          </Popconfirm> */}
          <Popconfirm title="确定要删除该方案吗？" okText="确定" cancelText="取消" onConfirm={() => handleDeletePlan(record)}>
            <a className="del">删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 表格列定义 - 提交方案
  const sharedPlanColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
      width: 60,
    },
    {
      title: '方案名称',
      dataIndex: 'planName',
      key: 'planName',
      render: (text, record) => <a onClick={() => handleOpenPlan(record)}>{text}</a>,
    },
    {
      title: '方案说明',
      dataIndex: 'planContent',
      key: 'planContent',
    },
    {
      title: '提交处室',
      // dataIndex: 'shareDepartment',
      // key: 'shareDepartment',
      dataIndex: 'planAttributionDepartment',
      key: 'planAttributionDepartment',
    },
    {
      title: '提交时间 ',
      dataIndex: 'shareTime',
      key: 'shareTime',
      render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '合并状态',
      dataIndex: 'mergeStatus',
      key: 'mergeStatus',
      render: (text, record) => {
        if (record.mergeStatus == 1) {
          return <React.Fragment>已合并 (合并次数：{record.mergeNum})</React.Fragment>;
        } else {
          return '未合并';
        }
      },
    },
    {
      title: '合并时间',
      dataIndex: 'mergeTime',
      key: 'mergeTime',
      render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => {
        if (record.mergeStatus == 0) {
          return <React.Fragment />;
        }
        return (
          <Space split={<Divider type="vertical" />}>
            <a onClick={() => handleRemergePlan(record)}>重新合并</a>
            <Popconfirm title="确定要取消合并该方案吗？" okText="确定" cancelText="取消" onConfirm={() => handleCancelMerge(record)}>
              <a>取消合并</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  // 待审核方案
  const PendingPlanColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
      width: 60,
    },
    {
      title: '方案名称',
      dataIndex: 'planName',
      key: 'planName',
      render: (text, record) => <a onClick={() => handleOpenPlan(record)}>{text}</a>,
    },
    {
      title: '方案说明',
      dataIndex: 'planContent',
      key: 'planContent',
    },
    {
      title: '提交处室',
      // dataIndex: 'shareDepartment',
      // key: 'shareDepartment',
      dataIndex: 'planAttributionDepartment',
      key: 'planAttributionDepartment',
    },
    {
      title: '提交时间 ',
      dataIndex: 'shareTime',
      key: 'shareTime',
      render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys, selectedRows) => {
      setSelectedRowKeys(keys);
      setSelectedPlans(selectedRows);
    },
    getCheckboxProps: record => ({
      // 现在都不禁用复选框
      // disabled: record.mergeStatus == 1, // 当mergeStatus为1（已合并）时禁用复选框
    }),
  };

  // =========== 渲染组件 ===========
  return (
    <div className={styles.container}>
      {/* 标签页 */}
      {pageType !== 'dqgxfa' && (
        <Tabs activeKey={activeTab} onChange={handleTabChange} className={styles.tabs}>
          <TabPane tab=" 我的动议" key="1"></TabPane>
          {/* {purview == 'A' && <TabPane tab="提交方案" key="2"></TabPane>}
          {purview == 'B' && <TabPane tab="工作方案" key="3"></TabPane>} */}
          {/* 现在不判断了 都要展示3个tab  */}
          <TabPane tab="待审核方案" key="4"></TabPane>
          <TabPane tab="待合稿的方案" key="2"></TabPane>
          <TabPane tab="考察上会任职" key="3"></TabPane>
        </Tabs>
      )}

      {/* 搜索区域 */}
      <div className={styles.searchArea}>
        <div className={styles.searchItem}>
          <span>方案名称：</span>
          <Input placeholder="请输入" value={searchParams.planName} onChange={e => setSearchParams({ ...searchParams, planName: e.target.value })} style={{ width: 200 }} autoComplete="off" />
        </div>
        <div className={styles.searchItem}>
          <span>起始时间：</span>
          <TimePicker regNowDate={false} style={{ width: 150 }} onChange={val => setSearchParams({ ...searchParams, planStartTime: moment(val).format('YYYY.MM.DD') })} />
          <span style={{ margin: '0 8px' }}>至</span>
          <span>结束时间：</span>
          <TimePicker regNowDate={false} style={{ width: 150 }} onChange={val => setSearchParams({ ...searchParams, planEndTime: moment(val).format('YYYY.MM.DD') })} />
        </div>
        <Space>
          <Button type="primary" onClick={handleSearch}>
            查 询
          </Button>
          <Button onClick={handleReset}>重 置</Button>
          {activeTab === '1' ? (
            <Button type="primary" onClick={showAddModal}>
              +新建任免方案
            </Button>
          ) : (
            <Button type="primary" onClick={showMergeModal} disabled={selectedRowKeys.length == 0}>
              合并方案
            </Button>
          )}
        </Space>
      </div>

      {/* 表格区域 */}
      {activeTab === '1' && <ListTable scroll={{ y: tableHeight }} rowKey="code" columns={myPlanColumns} data={planList} loading={loading} />}

      {activeTab === '2' && <ListTable scroll={{ y: tableHeight }} rowKey="code" columns={sharedPlanColumns} data={planList} loading={loading} rowSelection={rowSelection} />}

      {activeTab === '3' && <ListTable scroll={{ y: tableHeight }} rowKey="code" columns={myPlanColumns} data={planList} loading={loading} />}

      {activeTab === '4' && <ListTable scroll={{ y: tableHeight }} rowKey="code" columns={PendingPlanColumns} data={planList} loading={loading} rowSelection={rowSelection} />}
      {/* 分页组件 */}
      <ListPagination pagination={pagination} onChange={handlePageChange} showPageChange={true} />

      {/* 新增/编辑方案弹窗 */}
      <Modal
        title={isEdit ? '编辑方案' : '新增方案'}
        visible={addVisible}
        onCancel={handleAddCancel}
        footer={[
          <Button key="cancel" onClick={handleAddCancel}>
            取 消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleAddConfirm} loading={submitting}>
            确 认
          </Button>,
        ]}
        destroyOnClose
        maskClosable={false}
      >
        <Form form={form} layout="vertical" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck="false" data-form-type="other">
          {/* 隐藏字段欺骗浏览器自动填充 */}
          <Input style={{ display: 'none' }} name="planName" />
          <Input.Password style={{ display: 'none' }} name="planPassword" />
          <Form.Item name="planName" label="方案名称" rules={[{ required: true, message: '请输入方案名称' }]}>
            <Input placeholder="请输入方案名称" maxLength={50} autoComplete="off" />
          </Form.Item>
          {!isEdit ? (
            <Form.Item name="planPassword" label="方案密码" rules={[{ required: true, message: '请输入方案密码' }]}>
              <Input.Password placeholder="请输入方案密码" maxLength={20} autoComplete="new-password" autoCorrect="off" autoCapitalize="off" spellCheck="false" />
            </Form.Item>
          ) : (
            <Fragment>
              <Form.Item name="oldPassword" label="旧密码" rules={[{ required: passwordRequired, message: '请输入旧密码' }]}>
                <Input.Password
                  placeholder="请输入旧密码"
                  maxLength={20}
                  autoComplete="new-password"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck="false"
                  onChange={e => {
                    e.target.value ? setPasswordRequired(true) : setPasswordRequired(false);
                  }}
                />
              </Form.Item>
              <Form.Item name="newPassword" label="新密码" rules={[{ required: passwordRequired, message: '请输入新密码' }]}>
                <Input.Password
                  placeholder="请输入新密码"
                  maxLength={20}
                  autoComplete="new-password"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck="false"
                  onChange={e => {
                    e.target.value ? setPasswordRequired(true) : setPasswordRequired(false);
                  }}
                />
              </Form.Item>
            </Fragment>
          )}
          <Form.Item name="planContent" label="方案说明">
            <Input.TextArea placeholder="请输入备注说明" rows={4} maxLength={200} autoComplete="off" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 合并方案弹窗 */}
      <Modal
        title="新增合并方案"
        visible={mergeVisible}
        onCancel={handleMergeCancel}
        footer={[
          <Button key="cancel" onClick={handleMergeCancel}>
            取 消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleMergeConfirm} loading={submitting}>
            确 认
          </Button>,
        ]}
        destroyOnClose
        maskClosable={false}
        className={styles.mergeModal}
        width={600}
      >
        <Form form={mergeForm} layout="vertical" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck="false" data-form-type="other">
          {/* 隐藏字段欺骗浏览器自动填充 */}
          <input type="text" style={{ display: 'none' }} name="fakeusernameremembered" />
          <input type="password" style={{ display: 'none' }} name="fakepasswordremembered" />
          <Form.Item name="planName" label="方案名称" rules={[{ required: true, message: '请输入方案名称' }]}>
            <Input placeholder="请输入" maxLength={50} autoComplete="off" />
          </Form.Item>
          <Form.Item name="planPassword" label="方案密码" rules={[{ required: true, message: '请输入方案密码' }]}>
            <Input.Password placeholder="请输入方案密码" maxLength={20} autoComplete="new-password" autoCorrect="off" autoCapitalize="off" spellCheck="false" />
          </Form.Item>
          <Form.Item name="planContent" label="方案说明">
            <Input.TextArea placeholder="请输入备注" rows={4} maxLength={200} autoComplete="off" />
          </Form.Item>
          {selectedPlans.length > 0 && (
            <div className={styles.selectedPlans}>
              <h3>拟选择合并的方案：</h3>
              <ul>
                {selectedPlans.map(plan => (
                  <li key={plan.code}>{plan.planName}</li>
                ))}
              </ul>
            </div>
          )}
        </Form>
      </Modal>

      {/* 密码验证弹窗 */}
      <Modal
        title={`${currentPlan?.planName ? `当前方案: ${currentPlan.planName}` : '方案'}-密码验证`}
        visible={passwordVisible}
        onCancel={handlePasswordCancel}
        footer={[
          <Button key="cancel" onClick={handlePasswordCancel}>
            取 消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleVerifyPassword} loading={submitting}>
            确 认
          </Button>,
        ]}
        destroyOnClose
        maskClosable={false}
      >
        <Form form={passwordForm} layout="vertical" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck="false" data-form-type="other">
          {/* 隐藏字段欺骗浏览器自动填充 */}
          <input type="text" style={{ display: 'none' }} name="fakeusernameremembered" />
          <input type="password" style={{ display: 'none' }} name="fakepasswordremembered" />
          <Form.Item name="password" label="方案密码" rules={[{ required: true, message: '请输入方案密码' }]}>
            <Input.Password
              placeholder="请输入方案密码"
              autoFocus
              maxLength={20}
              autoComplete="new-password"
              autoCorrect="off"
              autoCapitalize="off"
              spellCheck="false"
              onPressEnter={handleVerifyPassword}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default connect(({ login, loading }: any) => ({
  login,
}))(AppointDismissPlan);
