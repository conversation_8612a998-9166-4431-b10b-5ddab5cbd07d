import React from 'react';
import styles from './edit.less';
import { ThemeContext } from './context';

export interface pType {
  unKey?: string;
  maxLen?: number;
  lineHeight?: number;
  maxHeight?: number;
  change?: (value?: object) => void | any;
  value?: any;
  style?: any;
}

class EditDiv extends React.Component<pType, { state: string }> {
  constructor(props) {
    super(props);
    this.state = {
      state: 'onBlur',
    };
  }
  static defaultProps = {
    unKey: new Date().valueOf(),
    maxLen: 0,
    lineHeight: 25,
  };
  onFocus = () => {
    this.setState(
      {
        state: 'focus',
      },
      () => {
        // this.getRange('refDiv2');
      }
    );
    // this['refDiv'].style.fontSize = '15px';
  };
  onBlur = () => {
    this.setState(
      {
        state: 'onBlur',
      },
      () => {
        this.getHeight('refDiv');
      }
    );
  };
  getRange = (key = 'refDiv2') => {
    let { innerText } = this[key];
    let { length } = innerText;
    let { maxLen } = this.props;
    if (maxLen && length > maxLen) {
      this[key].innerText = innerText.toString().substr(0, maxLen);
    }
  };
  getHeight = (key = 'refDiv') => {
    let { clientHeight, innerText, innerHTML } = this[key];
    let { length } = innerText;
    let { maxLen, lineHeight = 0, unKey = '', change, style } = this.props;
    // console.log('🚀 ~ EditDiv ~ clientHeight:', clientHeight, lineHeight, this[key].offsetHeight);
    if (clientHeight / lineHeight > 2) {
      this[key].style.fontSize = '12pt';
    } else {
      this[key].style.fontSize = '12pt';
    }
    //  else if (clientHeight / lineHeight > 1) {
    //   this[key].style.textAlign = 'left';
    // } else {
    //   if (style) {
    //     // this[key].style.textAlign = 'left';
    //   } else {
    //     this[key].style.textAlign = 'center';
    //   }
    // }
    if (maxLen && length > maxLen) {
      this[key].firstChild.innerText = innerText.toString().substr(0, maxLen);
    }
    change && change({ [unKey]: innerText });
  };
  componentDidMount() {
    this.getHeight('refDiv');
    console.log(this.props, "this.props");
  }

  render(): React.ReactNode {
    let { state } = this.state;
    const { value = '', style = {} } = this.props;
    return (
      <React.Fragment>
        {/* {state === 'onBlur' ? (
          <div ref={e => (this['refDiv'] = e)} className={styles.divEdit} tabIndex={0} onFocus={this.onFocus} suppressContentEditableWarning={true}>
            {value}1111111111
          </div>
        ) : (
          <div ref={e => (this['refDiv2'] = e)} className={styles.divEditFocus} contentEditable={true} onBlur={e => this.onBlur(e)} suppressContentEditableWarning={true}>
            {value}22222222222
          </div>
        )} */}
        <div
          ref={e => (this['refDiv'] = e)}
          className={`${state === 'onBlur' ? styles.divEdit : styles.divEditFocus} `}
          style={style}
          contentEditable={true}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          suppressContentEditableWarning={true}
        >
          {/* <span>{value}</span> */}
          {value}
        </div>
      </React.Fragment>
    );
  }
}
export default class Middleware extends React.Component<pType, {}> {
  static contextType = ThemeContext;
  render(): React.ReactNode {
    const { change, unKey = '' } = this.props;
    const { theme, toggle } = this.context;
    return (
      <EditDiv
        {...this.props}
        change={(e: any) => {
          change ? change(e[unKey]) : toggle(e);
        }}
      />
    );
  }
}
